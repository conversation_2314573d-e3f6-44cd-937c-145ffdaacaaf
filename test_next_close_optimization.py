#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script สำหรับ Next Close System Optimization
ทดสอบการทำงานของฟังก์ชัน optimal parameter testing ที่ปรับปรุงแล้ว
"""

import os
import sys
import pandas as pd
import numpy as np
import datetime
import traceback

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_next_close_parameter_functions():
    """
    ทดสอบฟังก์ชันต่างๆ ที่เกี่ยวข้องกับ Next Close System Optimization
    """
    print("="*80)
    print("🧪 ทดสอบ Next Close System Optimization Functions")
    print("="*80)
    
    try:
        # Import ฟังก์ชันที่ต้องการทดสอบ
        from LightGBM_11_4 import (
            find_optimal_threshold_next_close,
            find_optimal_nbars_sl_next_close,
            save_next_close_optimal_parameters,
            load_next_close_optimal_parameters,
            get_best_next_close_parameters,
            HORIZONS,
            USE_NEXT_CLOSE_SYSTEM,
            PARAM_DIST,
            STABLE_PARAM_DIST
        )
        
        print("✅ Import ฟังก์ชันสำเร็จ")
        
        # ตรวจสอบการตั้งค่า
        print(f"\n📊 การตั้งค่าปัจจุบัน:")
        print(f"   - USE_NEXT_CLOSE_SYSTEM: {USE_NEXT_CLOSE_SYSTEM}")
        print(f"   - HORIZONS: {HORIZONS}")
        print(f"   - PARAM_DIST keys: {list(PARAM_DIST.keys())}")
        print(f"   - STABLE_PARAM_DIST keys: {list(STABLE_PARAM_DIST.keys())}")
        
        # สร้างข้อมูลทดสอบ
        print(f"\n🔧 สร้างข้อมูลทดสอบ...")
        test_trade_df = create_sample_next_close_trades()
        
        if test_trade_df.empty:
            print("❌ ไม่สามารถสร้างข้อมูลทดสอบได้")
            return False
        
        print(f"✅ สร้างข้อมูลทดสอบสำเร็จ: {len(test_trade_df)} trades")
        print(f"   - Scenarios: {test_trade_df['Scenario'].unique()}")
        print(f"   - Horizons: {test_trade_df['Horizon'].unique()}")
        print(f"   - Trade Types: {test_trade_df['Trade Type'].unique()}")
        
        # ทดสอบ find_optimal_threshold_next_close
        print(f"\n🎯 ทดสอบ find_optimal_threshold_next_close...")
        threshold_results = find_optimal_threshold_next_close(
            test_trade_df, 
            symbol="GOLD", 
            timeframe="M60", 
            horizons=[5, 10, 15], 
            method="profit_factor"
        )
        
        if threshold_results:
            print("✅ ทดสอบ threshold optimization สำเร็จ")
            print(f"   - จำนวน scenarios: {len(threshold_results)}")
        else:
            print("⚠️ ไม่ได้ผลลัพธ์จาก threshold optimization")
        
        # ทดสอบ find_optimal_nbars_sl_next_close
        print(f"\n🎯 ทดสอบ find_optimal_nbars_sl_next_close...")
        nbars_results = find_optimal_nbars_sl_next_close(
            test_trade_df, 
            symbol="GOLD", 
            timeframe="M60", 
            horizons=[5, 10, 15], 
            nbars_range=range(2, 8)
        )
        
        if nbars_results:
            print("✅ ทดสอบ nBars_SL optimization สำเร็จ")
            print(f"   - จำนวน scenarios: {len(nbars_results)}")
        else:
            print("⚠️ ไม่ได้ผลลัพธ์จาก nBars_SL optimization")
        
        # ทดสอบการบันทึกและโหลด
        print(f"\n💾 ทดสอบการบันทึกและโหลดผลลัพธ์...")
        save_next_close_optimal_parameters(
            symbol="GOLD", 
            timeframe="M60", 
            threshold_results=threshold_results, 
            nbars_results=nbars_results
        )
        
        # ทดสอบการโหลด
        loaded_threshold, loaded_nbars = load_next_close_optimal_parameters("GOLD", "M60")
        
        if loaded_threshold and loaded_nbars:
            print("✅ ทดสอบการบันทึกและโหลดสำเร็จ")
        else:
            print("⚠️ การโหลดผลลัพธ์ไม่สมบูรณ์")
        
        # ทดสอบการดึงพารามิเตอร์ที่ดีที่สุด
        print(f"\n🏆 ทดสอบการดึงพารามิเตอร์ที่ดีที่สุด...")
        best_params = get_best_next_close_parameters("GOLD", "M60")
        
        if best_params:
            print("✅ ทดสอบการดึงพารามิเตอร์ที่ดีที่สุดสำเร็จ")
            print(f"   - Threshold: {best_params['threshold']:.4f}")
            print(f"   - nBars_SL: {best_params['nbars_sl']}")
            print(f"   - Source: {best_params['source']}")
        else:
            print("⚠️ ไม่สามารถดึงพารามิเตอร์ที่ดีที่สุดได้")
        
        print(f"\n✅ การทดสอบเสร็จสิ้น - ระบบทำงานได้ถูกต้อง")
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        traceback.print_exc()
        return False

def create_sample_next_close_trades():
    """
    สร้างข้อมูล trades ตัวอย่างสำหรับทดสอบ Next Close System
    """
    print("🔧 สร้างข้อมูล trades ตัวอย่าง...")
    
    np.random.seed(42)  # สำหรับผลลัพธ์ที่สม่ำเสมอ
    
    scenarios = ['trend_following', 'counter_trend']
    horizons = [5, 10, 15]
    trade_types = ['BUY', 'SELL']
    
    trades = []
    
    # สร้าง trades สำหรับแต่ละ scenario และ horizon
    for scenario in scenarios:
        for horizon in horizons:
            for trade_type in trade_types:
                # สร้าง 50 trades สำหรับแต่ละ combination
                for i in range(50):
                    # สร้าง profit แบบสุ่มที่มีลักษณะเหมือนจริง
                    if scenario == 'trend_following':
                        # Trend following มี win rate สูงกว่า แต่ profit เฉลี่ยต่ำกว่า
                        win_prob = 0.6
                        avg_win = 30
                        avg_loss = -20
                    else:
                        # Counter trend มี win rate ต่ำกว่า แต่ profit เฉลี่ยสูงกว่า
                        win_prob = 0.4
                        avg_win = 50
                        avg_loss = -25
                    
                    # ปรับตาม horizon
                    horizon_multiplier = horizon / 10.0
                    
                    if np.random.random() < win_prob:
                        profit = np.random.normal(avg_win * horizon_multiplier, 10)
                    else:
                        profit = np.random.normal(avg_loss * horizon_multiplier, 8)
                    
                    trades.append({
                        'Scenario': scenario,
                        'Horizon': horizon,
                        'Trade Type': trade_type,
                        'Profit': profit,
                        'Entry_Price': 2000 + np.random.normal(0, 50),
                        'Exit_Price': 2000 + profit/100 + np.random.normal(0, 50),
                        'Entry_Time': datetime.datetime.now() - datetime.timedelta(days=np.random.randint(1, 100)),
                        'Exit_Time': datetime.datetime.now() - datetime.timedelta(days=np.random.randint(1, 100))
                    })
    
    df = pd.DataFrame(trades)
    
    print(f"✅ สร้างข้อมูลตัวอย่างสำเร็จ: {len(df)} trades")
    print(f"   - Scenarios: {df['Scenario'].value_counts().to_dict()}")
    print(f"   - Horizons: {df['Horizon'].value_counts().to_dict()}")
    print(f"   - Trade Types: {df['Trade Type'].value_counts().to_dict()}")
    print(f"   - Profit range: {df['Profit'].min():.2f} to {df['Profit'].max():.2f}")
    
    return df

def test_parameter_compatibility():
    """
    ทดสอบความเข้ากันได้ของพารามิเตอร์ระหว่างระบบเก่าและใหม่
    """
    print("\n" + "="*80)
    print("🔄 ทดสอบความเข้ากันได้ของพารามิเตอร์")
    print("="*80)
    
    try:
        from LightGBM_11_4 import (
            USE_NEXT_CLOSE_SYSTEM,
            PARAM_DIST,
            STABLE_PARAM_DIST,
            MIN_TRADES_PER_SPLIT,
            MIN_TOTAL_TRADES,
            RELAXED_MIN_TOTAL_TRADES
        )
        
        print(f"📊 ระบบปัจจุบัน: {'Next Close System' if USE_NEXT_CLOSE_SYSTEM else 'Traditional System'}")
        
        # ตรวจสอบพารามิเตอร์ที่สำคัญ
        print(f"\n🔧 พารามิเตอร์ที่สำคัญ:")
        print(f"   - Learning Rate: {STABLE_PARAM_DIST['learning_rate']}")
        print(f"   - Num Leaves: {STABLE_PARAM_DIST['num_leaves']}")
        print(f"   - Min Data in Leaf: {STABLE_PARAM_DIST['min_data_in_leaf']}")
        print(f"   - Regularization α: {STABLE_PARAM_DIST['reg_alpha']}")
        print(f"   - Regularization λ: {STABLE_PARAM_DIST['reg_lambda']}")
        
        print(f"\n📊 ข้อกำหนดข้อมูล:")
        print(f"   - Min Trades per Split: {MIN_TRADES_PER_SPLIT}")
        print(f"   - Min Total Trades: {MIN_TOTAL_TRADES}")
        print(f"   - Relaxed Min Trades: {RELAXED_MIN_TOTAL_TRADES}")
        
        # ตรวจสอบความสมเหตุสมผลของพารามิเตอร์
        if USE_NEXT_CLOSE_SYSTEM:
            # Next Close System ควรมี learning rate ต่ำกว่า
            if STABLE_PARAM_DIST['learning_rate'][0] <= 0.05:
                print("✅ Learning rate เหมาะสมสำหรับ Next Close System")
            else:
                print("⚠️ Learning rate อาจสูงเกินไปสำหรับ Next Close System")
            
            # ควรมี min_data_in_leaf สูงกว่า
            if STABLE_PARAM_DIST['min_data_in_leaf'][0] >= 10:
                print("✅ Min data in leaf เหมาะสมสำหรับ Next Close System")
            else:
                print("⚠️ Min data in leaf อาจต่ำเกินไปสำหรับ Next Close System")
        
        print("✅ การทดสอบความเข้ากันได้เสร็จสิ้น")
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบความเข้ากันได้: {e}")
        return False

if __name__ == "__main__":
    print("🚀 เริ่มการทดสอบ Next Close System Optimization")
    
    # ทดสอบฟังก์ชันหลัก
    test1_success = test_next_close_parameter_functions()
    
    # ทดสอบความเข้ากันได้
    test2_success = test_parameter_compatibility()
    
    # สรุปผลการทดสอบ
    print("\n" + "="*80)
    print("📋 สรุปผลการทดสอบ")
    print("="*80)
    
    if test1_success and test2_success:
        print("🎉 การทดสอบทั้งหมดผ่าน - ระบบพร้อมใช้งาน")
        exit_code = 0
    else:
        print("❌ การทดสอบไม่ผ่าน - ต้องแก้ไขปัญหา")
        exit_code = 1
    
    print(f"   - ทดสอบฟังก์ชันหลัก: {'✅ ผ่าน' if test1_success else '❌ ไม่ผ่าน'}")
    print(f"   - ทดสอบความเข้ากันได้: {'✅ ผ่าน' if test2_success else '❌ ไม่ผ่าน'}")
    
    sys.exit(exit_code)
