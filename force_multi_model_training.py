#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
บังคับเทรน Multi-Model Architecture สำหรับ GBPUSD
เพื่อให้ได้โมเดลครบ 6 โมเดลตามที่ต้องการ
"""

import pandas as pd
import numpy as np
import os
import sys
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score

# Configuration
SYMBOL = "GBPUSD"
TIMEFRAME = "M60"
CSV_FILE = "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv"
MODELS_DIR = "LightGBM/Multi/models"

# LightGBM Parameters
LIGHTGBM_PARAMS = {
    'objective': 'binary',
    'metric': 'binary_logloss',
    'boosting_type': 'gbdt',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': -1,
    'random_state': 42
}

def create_improved_targets(df, horizon=5):
    """
    สร้าง Target_Buy และ Target_Sell ด้วยวิธี Flexible Method
    """
    print(f"🏗️ สร้าง Target_Buy และ Target_Sell...")
    
    df = df.copy()
    
    # สร้างข้อมูลราคาในอนาคต
    df['Next_Close'] = df['Close'].shift(-horizon)
    df['Price_Change_Pct'] = ((df['Next_Close'] - df['Close']) / df['Close']) * 100
    
    # ใช้ percentile แทนเงื่อนไขแข็ง (Flexible Method)
    buy_threshold = df['Price_Change_Pct'].quantile(0.6)  # 60th percentile
    sell_threshold = df['Price_Change_Pct'].quantile(0.4)  # 40th percentile
    
    print(f"📊 Target Thresholds: Buy={buy_threshold:.4f}%, Sell={sell_threshold:.4f}%")
    
    # สร้าง Target_Buy และ Target_Sell
    df['Target_Buy'] = (df['Price_Change_Pct'] > buy_threshold).astype(int)
    df['Target_Sell'] = (df['Price_Change_Pct'] < sell_threshold).astype(int)

    # สร้าง Target หลัก (รวม Buy และ Sell)
    df['Target'] = ((df['Target_Buy'] == 1) | (df['Target_Sell'] == 1)).astype(int)

    # ลบแถวที่มี NaN
    df.dropna(subset=['Next_Close', 'Target_Buy', 'Target_Sell', 'Target'], inplace=True)
    
    # แสดงสถิติ
    print(f"📊 Target Distribution:")
    print(f"   Target: {df['Target'].value_counts().to_dict()}")
    print(f"   Target_Buy: {df['Target_Buy'].value_counts().to_dict()}")
    print(f"   Target_Sell: {df['Target_Sell'].value_counts().to_dict()}")

    target_ratio = df['Target'].mean()
    buy_ratio = df['Target_Buy'].mean()
    sell_ratio = df['Target_Sell'].mean()
    print(f"   Target Ratio: {target_ratio:.3f} ({target_ratio*100:.1f}%)")
    print(f"   Buy Ratio: {buy_ratio:.3f} ({buy_ratio*100:.1f}%)")
    print(f"   Sell Ratio: {sell_ratio:.3f} ({sell_ratio*100:.1f}%)")
    
    return df

def create_market_scenarios(df):
    """
    สร้าง Market Scenarios ตาม EMA200
    """
    print(f"🏗️ สร้าง Market Scenarios...")
    
    # คำนวณ EMA200 ถ้ายังไม่มี
    if 'EMA200' not in df.columns:
        df['EMA200'] = df['Close'].ewm(span=200).mean()
    
    # สร้าง scenarios
    df['trend_following'] = (
        (df['Close'] > df['EMA200']) & (df['Low'] > df['EMA200'])
    ) | (
        (df['Close'] < df['EMA200']) & (df['High'] < df['EMA200'])
    )
    
    df['counter_trend'] = ~df['trend_following']
    
    # แสดงสถิติ
    trend_count = df['trend_following'].sum()
    counter_count = df['counter_trend'].sum()
    
    print(f"📊 Scenario Distribution:")
    print(f"   trend_following: {trend_count:,} ({trend_count/len(df)*100:.1f}%)")
    print(f"   counter_trend: {counter_count:,} ({counter_count/len(df)*100:.1f}%)")
    
    return df

def prepare_features(df):
    """
    เตรียม features สำหรับการเทรน
    """
    print(f"🏗️ เตรียม Features...")
    
    # Basic features
    feature_columns = []
    
    # Price features
    if 'Open' in df.columns:
        feature_columns.extend(['Open', 'High', 'Low', 'Close'])
    
    # Technical indicators
    technical_features = ['EMA200', 'RSI14', 'MACD_12_26_9', 'ATR_14', 'BB_width']
    for feat in technical_features:
        if feat in df.columns:
            feature_columns.append(feat)
    
    # ถ้าไม่มี features เพียงพอ ให้สร้างขึ้นมา
    if len(feature_columns) < 10:
        # สร้าง lag features
        for lag in [1, 2, 3, 5]:
            df[f'Close_Lag_{lag}'] = df['Close'].shift(lag)
            feature_columns.append(f'Close_Lag_{lag}')
        
        # สร้าง moving averages
        for window in [5, 10, 20]:
            df[f'Close_MA_{window}'] = df['Close'].rolling(window).mean()
            feature_columns.append(f'Close_MA_{window}')
    
    # ลบ NaN
    df.dropna(inplace=True)
    
    # เลือกเฉพาะ features ที่มีอยู่
    available_features = [col for col in feature_columns if col in df.columns]
    
    print(f"📊 Available Features: {len(available_features)}")
    print(f"   Features: {available_features[:10]}...")  # แสดงแค่ 10 ตัวแรก
    
    return df, available_features

def train_scenario_model(df, scenario_name, target_col, features, min_samples=50):
    """
    เทรนโมเดลสำหรับ scenario เฉพาะ
    """
    print(f"\n🔄 เทรน {scenario_name} model...")
    
    # กรองข้อมูลตาม scenario
    if scenario_name.startswith('trend_following'):
        scenario_data = df[df['trend_following']].copy()
    elif scenario_name.startswith('counter_trend'):
        scenario_data = df[df['counter_trend']].copy()
    else:
        scenario_data = df.copy()
    
    print(f"📊 Scenario data: {len(scenario_data):,} rows")
    
    # ตรวจสอบข้อมูลเพียงพอ
    if len(scenario_data) < min_samples:
        print(f"⚠️ ข้อมูลไม่เพียงพอ: {len(scenario_data)} < {min_samples}")
        return None
    
    # เตรียม target
    y = scenario_data[target_col].copy()
    
    # ตรวจสอบ class distribution
    class_counts = y.value_counts()
    print(f"📊 Class distribution: {class_counts.to_dict()}")
    
    if len(class_counts) < 2:
        print(f"⚠️ มี class เดียว: {class_counts.to_dict()}")
        return None
    
    min_class_count = min(class_counts)
    if min_class_count < 5:
        print(f"⚠️ Class ที่น้อยที่สุดมี {min_class_count} samples < 5")
        return None
    
    # เตรียม features
    X = scenario_data[features].copy()
    
    # แบ่งข้อมูล
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # เทรนโมเดล
    model = lgb.LGBMClassifier(**LIGHTGBM_PARAMS)
    model.fit(X_train_scaled, y_train)
    
    # ประเมินผล
    y_pred = model.predict(X_test_scaled)
    y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
    
    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_pred_proba)
    
    print(f"📊 Performance: Accuracy={accuracy:.3f}, F1={f1:.3f}, AUC={auc:.3f}")
    
    return {
        'model': model,
        'scaler': scaler,
        'features': features,
        'performance': {
            'accuracy': accuracy,
            'f1': f1,
            'auc': auc,
            'samples': len(scenario_data)
        }
    }

def save_model(result, scenario_name, symbol, timeframe):
    """
    บันทึกโมเดล
    """
    if result is None:
        print(f"❌ ไม่สามารถบันทึก {scenario_name} - โมเดลเป็น None")
        return False
    
    # สร้างโฟลเดอร์
    model_dir = os.path.join(MODELS_DIR, scenario_name)
    os.makedirs(model_dir, exist_ok=True)
    
    # บันทึกไฟล์
    prefix = f"{timeframe}_{symbol}"
    
    try:
        joblib.dump(result['model'], os.path.join(model_dir, f"{prefix}_trained.pkl"))
        joblib.dump(result['scaler'], os.path.join(model_dir, f"{prefix}_scaler.pkl"))
        joblib.dump(result['features'], os.path.join(model_dir, f"{prefix}_features.pkl"))
        
        print(f"✅ บันทึก {scenario_name} สำเร็จ")
        return True
    except Exception as e:
        print(f"❌ ไม่สามารถบันทึก {scenario_name}: {e}")
        return False

def main():
    """
    ฟังก์ชันหลัก
    """
    print("="*80)
    print("🚀 บังคับเทรน Multi-Model Architecture สำหรับ GBPUSD")
    print("="*80)
    
    # อ่านข้อมูล
    print(f"📂 อ่านข้อมูลจาก: {CSV_FILE}")
    df = pd.read_csv(CSV_FILE, low_memory=False)
    
    # แปลงคอลัมน์ราคาเป็นตัวเลข
    price_columns = ['Open', 'High', 'Low', 'Close']
    for col in price_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    print(f"📊 ข้อมูลเริ่มต้น: {len(df):,} rows")
    
    # สร้าง targets
    df = create_improved_targets(df)
    print(f"📊 หลังสร้าง targets: {len(df):,} rows")
    
    # สร้าง scenarios
    df = create_market_scenarios(df)
    
    # เตรียม features
    df, features = prepare_features(df)
    print(f"📊 หลังเตรียม features: {len(df):,} rows")
    
    # กำหนด scenarios และ targets
    scenarios = [
        ('trend_following', 'Target'),
        ('counter_trend', 'Target'),
        ('trend_following_Buy', 'Target_Buy'),
        ('trend_following_Sell', 'Target_Sell'),
        ('counter_trend_Buy', 'Target_Buy'),
        ('counter_trend_Sell', 'Target_Sell')
    ]
    
    # เทรนแต่ละ scenario
    results = {}
    successful_models = 0
    
    for scenario_name, target_col in scenarios:
        print(f"\n{'='*60}")
        print(f"🔄 เทรน {scenario_name}")
        print(f"{'='*60}")
        
        if target_col not in df.columns:
            print(f"❌ ไม่พบ {target_col} column")
            continue
        
        result = train_scenario_model(df, scenario_name, target_col, features)
        
        if result is not None:
            results[scenario_name] = result
            if save_model(result, scenario_name, SYMBOL, TIMEFRAME):
                successful_models += 1
        else:
            print(f"❌ ไม่สามารถเทรน {scenario_name}")
    
    # สรุปผล
    print(f"\n{'='*80}")
    print(f"📋 สรุปผลการเทรน Multi-Model Architecture")
    print(f"{'='*80}")
    print(f"🎯 เป้าหมาย: 6 โมเดล")
    print(f"✅ สำเร็จ: {successful_models} โมเดล")
    print(f"❌ ล้มเหลว: {6 - successful_models} โมเดล")
    
    if successful_models == 6:
        print(f"🎉 สำเร็จ! ได้โมเดลครบ 6 โมเดลแล้ว")
    else:
        print(f"⚠️ ยังไม่ครบ - ต้องปรับปรุงเพิ่มเติม")
    
    # แสดงรายละเอียดโมเดลที่สำเร็จ
    for scenario_name, result in results.items():
        perf = result['performance']
        print(f"   {scenario_name}: Samples={perf['samples']:,}, "
              f"F1={perf['f1']:.3f}, AUC={perf['auc']:.3f}")

if __name__ == "__main__":
    main()
