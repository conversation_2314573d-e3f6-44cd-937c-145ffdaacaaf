import pandas as pd
from LightGBM_11_2 import create_trade_cycles_with_next_close, get_training_run_count, increment_training_run_count
from financial_integration import create_sample_trade_data

symbol = 'GOLD'
timeframe = 'M60'

print('current training_runs =', get_training_run_count())

# use sample df for quick test
sample_df = create_sample_trade_data(symbol, timeframe)
# normalize column names from create_sample_trade_data
cols = list(sample_df.columns)
if 'entry_time' in cols:
    ts = pd.to_datetime(sample_df['entry_time'])
elif 'entry_time' not in cols and 'entry_time' in sample_df.columns.str.lower():
    # unlikely, but try lower-cased search
    for c in sample_df.columns:
        if c.lower() == 'entry_time':
            ts = pd.to_datetime(sample_df[c])
            break
else:
    # fallback: use now-based index
    ts = pd.date_range('2025-01-01', periods=len(sample_df), freq='H')

# ensure ts is a pandas Series so we can use .dt.strftime safely
if not isinstance(ts, pd.Series):
    ts = pd.Series(ts)

sample_df['Date'] = ts.dt.strftime('%Y-%m-%d')
sample_df['Time'] = ts.dt.strftime('%H:%M:%S')
if 'entry_price' in cols:
    sample_df['Close'] = sample_df['entry_price']
elif 'entry_price' not in cols and 'entry_price' in sample_df.columns.str.lower():
    for c in sample_df.columns:
        if c.lower() == 'entry_price':
            sample_df['Close'] = sample_df[c]
            break
else:
    sample_df['Close'] = 0.0
# create Next_Close_5/10/15 as shifted Close for demo
for h in [5,10,15]:
    sample_df[f'Next_Close_{h}'] = sample_df['Close'] + (h*0.01)

trade_df, stats = create_trade_cycles_with_next_close(sample_df, scenario_models=None, model_features=None, symbol=symbol, timeframe=timeframe, model_confidence_threshold=0.5)
print('trade_df.shape=', trade_df.shape)
print('stats=', stats)
