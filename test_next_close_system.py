#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ไฟล์ทดสอบระบบ Next Close System ใหม่
สำหรับทดสอบการทำงานของระบบที่ปรับปรุงแล้ว

การใช้งาน:
python test_next_close_system.py

หรือเปิดใช้งานใน LightGBM_11_2.py โดยตั้งค่า:
USE_NEXT_CLOSE_SYSTEM = True
USE_MULTI_HORIZON_DECISION = True
"""

import pandas as pd
import numpy as np
import os
import sys
import json
from datetime import datetime, timedelta

# เพิ่ม path สำหรับ import จาก LightGBM_11_2.py
sys.path.append('.')

def create_sample_data(symbol='GBPUSD', timeframe='M60', num_rows=1000):
    """
    สร้างข้อมูลตัวอย่างสำหรับทดสอบ
    """
    print(f"🔧 สร้างข้อมูลตัวอย่าง: {symbol} {timeframe} ({num_rows} rows)")
    
    # สร้างข้อมูลราคาแบบสุ่ม
    np.random.seed(42)
    
    # เริ่มต้นด้วยราคา base
    base_price = 1.3000 if 'USD' in symbol else 2000.0
    
    # สร้าง datetime
    start_date = datetime(2024, 1, 1)
    dates = [start_date + timedelta(hours=i) for i in range(num_rows)]
    
    # สร้างราคา OHLC
    prices = []
    current_price = base_price
    
    for i in range(num_rows):
        # Random walk สำหรับราคา
        change = np.random.normal(0, 0.001)  # volatility 0.1%
        current_price += change
        
        # สร้าง OHLC
        open_price = current_price
        high_price = open_price + abs(np.random.normal(0, 0.0005))
        low_price = open_price - abs(np.random.normal(0, 0.0005))
        close_price = open_price + np.random.normal(0, 0.0003)
        
        # ปรับให้ high/low สมเหตุสมผล
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        prices.append({
            'Date': dates[i].strftime('%Y.%m.%d'),
            'Time': dates[i].strftime('%H:%M'),
            'Open': round(open_price, 5),
            'High': round(high_price, 5),
            'Low': round(low_price, 5),
            'Close': round(close_price, 5),
            'Volume': np.random.randint(100, 1000)
        })
        
        current_price = close_price
    
    df = pd.DataFrame(prices)
    
    # เพิ่ม DateTime column
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    
    print(f"✅ สร้างข้อมูลตัวอย่างเสร็จสิ้น: {len(df)} rows")
    print(f"   ราคาเริ่มต้น: {df['Close'].iloc[0]:.5f}")
    print(f"   ราคาสุดท้าย: {df['Close'].iloc[-1]:.5f}")
    
    return df

def test_create_features():
    """
    ทดสอบฟังก์ชัน create_features ที่ปรับปรุงแล้ว
    """
    print(f"\n{'='*60}")
    print(f"🧪 ทดสอบ create_features")
    print(f"{'='*60}")
    
    # สร้างข้อมูลตัวอย่าง
    df = create_sample_data()
    
    try:
        # Import ฟังก์ชันจาก LightGBM_11_2.py
        from LightGBM_11_2 import create_features
        
        # เรียกใช้ฟังก์ชัน
        df_with_features = create_features(df, 'GBPUSD', 'M60', 4)
        
        # ตรวจสอบผลลัพธ์
        print(f"✅ สร้าง features สำเร็จ")
        print(f"   จำนวนคอลัมน์: {len(df_with_features.columns)}")
        
        # ตรวจสอบ Next_Close columns
        next_close_cols = [col for col in df_with_features.columns if 'Next_Close' in col]
        print(f"   Next_Close columns: {next_close_cols}")
        
        for col in next_close_cols:
            valid_count = df_with_features[col].notna().sum()
            print(f"   - {col}: {valid_count} valid values")
        
        return df_with_features
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_next_close_system():
    """
    ทดสอบระบบ Next Close System
    """
    print(f"\n{'='*60}")
    print(f"🧪 ทดสอบ Next Close System")
    print(f"{'='*60}")
    
    # สร้างข้อมูลที่มี features แล้ว
    df = test_create_features()
    if df is None:
        return
    
    try:
        # Import ฟังก์ชันจาก LightGBM_11_2.py
        from LightGBM_11_2 import create_trade_cycles_with_next_close
        
        # เรียกใช้ฟังก์ชัน
        trade_df, stats = create_trade_cycles_with_next_close(
            df, None, None, 'GBPUSD', 'M60', 0.5, None, None, [5, 10, 15]
        )
        
        # ตรวจสอบผลลัพธ์
        if not trade_df.empty:
            print(f"✅ สร้าง trades สำเร็จ: {len(trade_df)} trades")
            
            # แสดงสถิติ
            print(f"\n📊 สถิติ Trades:")
            print(f"   - Scenarios: {trade_df['Scenario'].value_counts().to_dict()}")
            print(f"   - Horizons: {trade_df['Horizon'].value_counts().to_dict()}")
            print(f"   - Trade Types: {trade_df['Trade Type'].value_counts().to_dict()}")
            
            # แสดงตัวอย่าง trades
            print(f"\n📋 ตัวอย่าง Trades (5 แถวแรก):")
            display_cols = ['Entry Time', 'Trade Type', 'Scenario', 'Horizon', 'Profit']
            print(trade_df[display_cols].head())
            
            # แสดงสถิติจาก stats
            if 'summary' in stats:
                summary = stats['summary']
                print(f"\n📈 สถิติรวม:")
                print(f"   - Total Trades: {summary.get('total_trades', 0)}")
                print(f"   - Win Rate: {summary.get('win_rate', 0):.2%}")
                print(f"   - Total Profit: {summary.get('total_profit', 0):.2f}")
                print(f"   - Expectancy: {summary.get('expectancy', 0):.2f}")
                print(f"   - Profit Factor: {summary.get('profit_factor', 0):.2f}")
            
            return trade_df, stats
        else:
            print(f"⚠️ ไม่พบ trades")
            return None, None
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_multi_horizon_decision():
    """
    ทดสอบระบบ Multi-Horizon Decision
    """
    print(f"\n{'='*60}")
    print(f"🧪 ทดสอบ Multi-Horizon Decision System")
    print(f"{'='*60}")
    
    # สร้างข้อมูลที่มี features แล้ว
    df = test_create_features()
    if df is None:
        return
    
    try:
        # Import ฟังก์ชันจาก LightGBM_11_2.py
        from LightGBM_11_2 import create_multi_horizon_decision_system
        
        # เรียกใช้ฟังก์ชัน
        trade_df, stats, decision_summary = create_multi_horizon_decision_system(
            df, None, None, 'GBPUSD', 'M60', 0.5, [5, 10, 15], '2of3'
        )
        
        # ตรวจสอบผลลัพธ์
        if not trade_df.empty:
            print(f"✅ Multi-Horizon Decision สำเร็จ: {len(trade_df)} trades")
            
            # แสดง Decision Summary
            print(f"\n🎯 Decision Summary:")
            for scenario, decision_data in decision_summary['decisions'].items():
                decision = "TRADE" if decision_data['final_decision'] == 1 else "HOLD"
                confidence = decision_summary['confidence_scores'][scenario]
                votes = decision_data['vote_summary']
                
                print(f"   - {scenario}: {decision} (Confidence: {confidence:.3f}, Votes: {votes})")
            
            # บันทึกผลลัพธ์
            output_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'trade_count': len(trade_df),
                    'stats': stats,
                    'decision_summary': decision_summary
                }, f, indent=4, ensure_ascii=False, default=str)
            
            print(f"💾 บันทึกผลลัพธ์: {output_file}")
            
            return trade_df, stats, decision_summary
        else:
            print(f"⚠️ ไม่พบ trades")
            return None, None, None
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def test_target_creation():
    """
    ทดสอบการสร้าง targets ด้วยระบบใหม่
    """
    print(f"\n{'='*60}")
    print(f"🧪 ทดสอบ Target Creation")
    print(f"{'='*60}")
    
    # สร้าง trade data ตัวอย่าง
    trade_df, _ = test_next_close_system()
    if trade_df is None or trade_df.empty:
        return
    
    try:
        # Import ฟังก์ชันจาก LightGBM_11_2.py
        from LightGBM_11_2 import process_trade_targets_next_close
        
        # เรียกใช้ฟังก์ชัน
        df_with_targets = process_trade_targets_next_close(trade_df, 'GBPUSD', 'M60', [5, 10, 15])
        
        # ตรวจสอบผลลัพธ์
        if not df_with_targets.empty:
            print(f"✅ สร้าง targets สำเร็จ")
            
            # แสดง target columns
            target_cols = [col for col in df_with_targets.columns if 'Target' in col]
            print(f"   Target columns: {target_cols}")
            
            # แสดงการกระจายของแต่ละ target
            for col in target_cols:
                if col in df_with_targets.columns:
                    value_counts = df_with_targets[col].value_counts()
                    print(f"   - {col}: {value_counts.to_dict()}")
            
            return df_with_targets
        else:
            print(f"⚠️ ไม่สามารถสร้าง targets ได้")
            return None
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    ฟังก์ชันหลักสำหรับทดสอบ
    """
    print(f"🚀 เริ่มทดสอบระบบ Next Close System")
    print(f"เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ทดสอบแต่ละส่วน
    test_create_features()
    test_next_close_system()
    test_multi_horizon_decision()
    test_target_creation()
    
    print(f"\n🎉 ทดสอบเสร็จสิ้น!")
    print(f"💡 หากต้องการใช้ระบบใหม่ใน LightGBM_11_2.py ให้ตั้งค่า:")
    print(f"   USE_NEXT_CLOSE_SYSTEM = True")
    print(f"   USE_MULTI_HORIZON_DECISION = True")

if __name__ == "__main__":
    main()
