#!/usr/bin/env python3
"""
Financial Integration Module
โมดูลสำหรับผสานรวมการวิเคราะห์ทางการเงินกับระบบเทรด

ใช้ร่วมกับ create_trade_cycles_with_model() เพื่อวิเคราะห์ผลการเทรด
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

# Import Financial Analysis System
from financial_analysis_system import FinancialAnalysisSystem

# ราคาปัจจุบันสำหรับการคำนวณ (ควรอัปเดตเป็นราคาจริง)
CURRENT_PRICES = {
    'GOLD': 2650.0,
    'EURUSD': 1.0850,
    'GBPUSD': 1.2650,
    'AUDUSD': 0.6750,
    'NZDUSD': 0.6150,
    'USDCAD': 1.3550,
    'USDJPY': 148.50
}

# กลุ่มสัญลักษณ์ที่ต้องวิเคราะห์
TEST_GROUPS = ['AUDUSD', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
TIMEFRAMES = ['M30', 'M60']

def integrate_with_trade_cycles(symbol: str, timeframe: str, trade_cycles_df: pd.DataFrame, 
                               financial_system: FinancialAnalysisSystem) -> bool:
    """
    ผสานรวมผลจาก create_trade_cycles_with_model() กับระบบวิเคราะห์ทางการเงิน
    
    Args:
        symbol: สัญลักษณ์ (เช่น EURUSD, GOLD)
        timeframe: ไทม์เฟรม (M30, M60)
        trade_cycles_df: DataFrame จาก create_trade_cycles_with_model()
        financial_system: ระบบวิเคราะห์ทางการเงิน
        
    Returns:
        bool: สำเร็จหรือไม่
    """
    
    print(f"🔄 กำลังประมวลผล {symbol} {timeframe}...")
    
    # ตรวจสอบข้อมูล
    if trade_cycles_df.empty:
        print(f"⚠️ ไม่มีข้อมูลการเทรดสำหรับ {symbol} {timeframe}")
        return False
    
    # แปลงข้อมูลให้เข้ากับระบบวิเคราะห์ทางการเงิน
    processed_trades = []

    # ยืดหยุ่นกับชื่อคอลัมน์หลายรูปแบบ
    def pick(row, names, default=None):
        for n in names:
            if n in row:
                return row.get(n)
        return default

    # print(f"🔍 Debug: trade_cycles_df columns = {list(trade_cycles_df.columns)}")
    # print(f"🔍 Debug: trade_cycles_df shape = {trade_cycles_df.shape}")

    # Debug: แสดงขนาดและตัวอย่างของ trade_cycles_df
    try:
        print(f"   Debug: trade_cycles_df shape={trade_cycles_df.shape}, columns={list(trade_cycles_df.columns)}")
        if len(trade_cycles_df) > 0:
            print("   Debug: sample rows:\n", trade_cycles_df.head(3).to_string())
    except Exception:
        pass

    for idx, row in trade_cycles_df.iterrows():
        try:
            # แมปคอลัมน์จากหลายรูปแบบ (case-insensitive-like)
            entry_time = pick(row, ['Entry Time', 'EntryTime', 'entry_time', 'Entry_Time', 'EntryTime'], datetime.now())
            exit_time = pick(row, ['Exit Time', 'ExitTime', 'exit_time', 'Exit_Time'], datetime.now())
            entry_price = pick(row, ['Entry Price', 'EntryPrice', 'entry_price', 'Entry_Price'], 0)
            exit_price = pick(row, ['Exit Price', 'ExitPrice', 'exit_price', 'Exit_Price'], 0)
            trade_type = pick(row, ['Trade Type', 'TradeType', 'trade_type', 'trade'], 'BUY')
            # profit can be provided as Profit, profit_pips, Profit_Pips or computed from prices
            profit = pick(row, ['Profit', 'profit', 'profit_usd', 'Profit_USD'], None)
            pips_profit_in = pick(row, ['pips_profit', 'profit_pips', 'pip_profit', 'pips'], None)

            # แปลง string เป็น datetime ถ้าจำเป็น
            if isinstance(entry_time, str):
                try:
                    entry_time = pd.to_datetime(entry_time)
                except:
                    entry_time = datetime.now()

            if isinstance(exit_time, str):
                try:
                    exit_time = pd.to_datetime(exit_time)
                except:
                    exit_time = datetime.now()

            # คำนวณ pips_profit จากข้อมูลที่มี (รองรับหลายรูปแบบ)
            pips_profit = 0
            if pips_profit_in is not None:
                # ถ้ามี pips profit โดยตรง
                try:
                    pips_profit = float(pips_profit_in)
                except Exception:
                    pips_profit = 0
            elif profit is not None:
                # ถ้ามี profit เป็น USD ให้ประมาณเป็น pips
                try:
                    profit_val = float(profit)
                    if symbol == 'GOLD':
                        pips_profit = profit_val
                    elif symbol in ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD']:
                        pips_profit = profit_val / 10.0
                    elif symbol in ['USDJPY', 'USDCAD']:
                        pips_profit = profit_val / 7.0
                    else:
                        pips_profit = profit_val
                except Exception:
                    pips_profit = 0
            elif entry_price and exit_price:
                try:
                    if symbol == 'GOLD':
                        pips_profit = (float(exit_price) - float(entry_price)) * 100 if str(trade_type).upper().startswith('B') else (float(entry_price) - float(exit_price)) * 100
                    elif symbol in ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD']:
                        pips_profit = (float(exit_price) - float(entry_price)) * 10000 if str(trade_type).upper().startswith('B') else (float(entry_price) - float(exit_price)) * 10000
                    elif symbol in ['USDJPY', 'USDCAD']:
                        pips_profit = (float(exit_price) - float(entry_price)) * 100 if str(trade_type).upper().startswith('B') else (float(entry_price) - float(exit_price)) * 100
                    else:
                        pips_profit = 0
                except Exception:
                    pips_profit = 0

            # สร้างข้อมูลการเทรด
            trade_data = {
                'open_time': entry_time,
                'close_time': exit_time,
                'direction': trade_type,
                'open_price': entry_price,
                'close_price': exit_price,
                'pips_profit': pips_profit
            }

            processed_trades.append(trade_data)

        except Exception as e:
            print(f"⚠️ Error processing trade {idx}: {e}")
            # print(f"   Row data: {dict(row)}")
            continue
    
    if not processed_trades:
        print(f"⚠️ ไม่สามารถประมวลผลข้อมูลการเทรดสำหรับ {symbol} {timeframe} - processed_trades empty")
        return False
    
    # สร้าง DataFrame สำหรับการวิเคราะห์
    trades_df = pd.DataFrame(processed_trades)
    
    # ประมวลผลด้วยระบบวิเคราะห์ทางการเงิน
    analysis_result = financial_system.process_trade_cycle(
        symbol=symbol,
        timeframe=timeframe,
        trade_data=trades_df,
        current_prices=CURRENT_PRICES
    )
    
    if analysis_result:
        # บันทึกผลการวิเคราะห์แต่ละสัญลักษณ์
        financial_system.save_individual_analysis(symbol, timeframe, analysis_result)
        print(f"✅ ประมวลผล {symbol} {timeframe} สำเร็จ: {len(processed_trades)} รายการ")
        return True
    else:
        print(f"❌ ไม่สามารถประมวลผล {symbol} {timeframe}")
        return False

def run_complete_financial_analysis(account_balance: float = 1000):
    """
    รันการวิเคราะห์ทางการเงินแบบสมบูรณ์สำหรับทุกสัญลักษณ์และไทม์เฟรม
    
    Args:
        account_balance: ยอดเงินในบัญชี (USD)
    """
    
    print("🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์")
    print(f"💰 ยอดเงินในบัญชี: ${account_balance:,.2f}")
    print(f"📊 สัญลักษณ์ที่วิเคราะห์: {', '.join(TEST_GROUPS)}")
    print(f"⏰ ไทม์เฟรม: {', '.join(TIMEFRAMES)}")
    print("=" * 60)
    
    # สร้างระบบวิเคราะห์ทางการเงิน
    financial_system = FinancialAnalysisSystem(base_currency='USD', leverage=500)
    
    # ประมวลผลแต่ละสัญลักษณ์และไทม์เฟรม
    processed_count = 0
    total_combinations = len(TEST_GROUPS) * len(TIMEFRAMES)
    
    for symbol in TEST_GROUPS:
        for timeframe in TIMEFRAMES:
            print(f"\n📈 กำลังประมวลผล {symbol} {timeframe}...")
            
            # *** ที่นี่คุณต้องเรียกใช้ create_trade_cycles_with_model() ***
            # trade_cycles_df = create_trade_cycles_with_model(symbol, timeframe)
            
            # สำหรับตัวอย่าง ใช้ข้อมูลจำลอง
            # ในการใช้งานจริง ให้แทนที่ด้วยการเรียกใช้ฟังก์ชันจริง
            trade_cycles_df = create_sample_trade_data(symbol, timeframe)
            
            # ประมวลผลด้วยระบบวิเคราะห์ทางการเงิน
            success = integrate_with_trade_cycles(symbol, timeframe, trade_cycles_df, financial_system)
            
            if success:
                processed_count += 1
            
            print(f"📊 ความคืบหน้า: {processed_count}/{total_combinations}")
    
    print(f"\n✅ ประมวลผลเสร็จสิ้น: {processed_count}/{total_combinations} รายการ")
    
    # รันการวิเคราะห์ทั้งหมด
    if processed_count > 0:
        results = financial_system.run_complete_analysis(account_balance)
        
        print("\n🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!")
        print(f"📁 ผลลัพธ์บันทึกที่: {financial_system.results_folder}")
        
        return results
    else:
        print("❌ ไม่มีข้อมูลสำหรับการวิเคราะห์")
        return None

def create_sample_trade_data(symbol: str, timeframe: str) -> pd.DataFrame:
    """
    สร้างข้อมูลการเทรดจำลองสำหรับการทดสอบ
    *** ในการใช้งานจริง ให้ลบฟังก์ชันนี้และใช้ข้อมูลจริงจาก create_trade_cycles_with_model() ***
    """
    
    # สร้างข้อมูลจำลอง
    np.random.seed(42)
    num_trades = np.random.randint(10, 50)  # 10-50 รายการ
    
    trades = []
    base_time = datetime.now()
    
    for i in range(num_trades):
        # สร้างเวลาการเทรด
        entry_time = base_time - pd.Timedelta(days=num_trades-i)
        exit_time = entry_time + pd.Timedelta(hours=np.random.randint(1, 24))
        
        # สร้างราคาและกำไร/ขาดทุน
        direction = np.random.choice(['BUY', 'SELL'])
        
        if symbol == 'GOLD':
            entry_price = 2650 + np.random.normal(0, 20)
            profit_pips = np.random.normal(5, 15)  # เฉลี่ย 5 pips
        else:
            if symbol in ['EURUSD', 'GBPUSD']:
                entry_price = 1.1 + np.random.normal(0, 0.05)
            elif symbol in ['USDJPY']:
                entry_price = 148 + np.random.normal(0, 2)
            else:
                entry_price = 1.0 + np.random.normal(0, 0.1)
            
            profit_pips = np.random.normal(3, 10)  # เฉลี่ย 3 pips
        
        exit_price = entry_price + (profit_pips * 0.0001 if symbol != 'GOLD' else profit_pips * 0.01)
        
        trades.append({
            'entry_time': entry_time,
            'exit_time': exit_time,
            'direction': direction,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'profit_pips': profit_pips
        })
    
    return pd.DataFrame(trades)

if __name__ == "__main__":
    # รันการวิเคราะห์ทางการเงินแบบสมบูรณ์
    results = run_complete_financial_analysis(account_balance=1000)
    
    if results:
        print("\n🎯 การวิเคราะห์เสร็จสมบูรณ์!")
        print("📊 ตรวจสอบไฟล์ผลลัพธ์ในโฟลเดอร์ Financial_Analysis_Results")
    else:
        print("\n❌ การวิเคราะห์ไม่สำเร็จ")
