#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Test Parameter Management Endpoints
=====================================

สคริปต์สำหรับทดสอบ endpoint การจัดการพารามิเตอร์ใน WebRequest_Server_06.py
"""

import requests
import json
import time
from datetime import datetime

# การตั้งค่า Server
SERVER_HOST = "127.0.0.1"
SERVER_PORT = 54321
BASE_URL = f"http://{SERVER_HOST}:{SERVER_PORT}"

def test_get_parameters():
    """ทดสอบการดึงพารามิเตอร์ปัจจุบัน"""
    print(f"\n🔍 Testing GET /get_parameters")
    print(f"=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/get_parameters")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {data['status']}")
            print(f"📝 Message: {data['message']}")
            print(f"🕒 Timestamp: {data['timestamp']}")
            print(f"\n📊 Current Parameters:")
            for key, value in data['parameters'].items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_parameter_status():
    """ทดสอบการดูสถานะพารามิเตอร์แบบละเอียด"""
    print(f"\n🔍 Testing GET /parameter_status")
    print(f"=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/parameter_status")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {data['status']}")
            print(f"📝 Message: {data['message']}")
            print(f"🕒 Timestamp: {data['timestamp']}")
            print(f"📁 Optimization file available: {data['optimization_file_available']}")
            
            print(f"\n📊 Current Parameters:")
            for key, value in data['current_parameters'].items():
                print(f"   {key}: {value}")
            
            if data['optimization_file_available']:
                print(f"\n🎯 Optimization Parameters:")
                for key, value in data['optimization_parameters'].items():
                    print(f"   {key}: {value}")
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_update_parameters():
    """ทดสอบการอัปเดตพารามิเตอร์"""
    print(f"\n🔄 Testing POST /update_parameters")
    print(f"=" * 50)
    
    try:
        payload = {
            "symbol": "USDJPY",
            "timeframe": "M60"
        }
        
        response = requests.post(
            f"{BASE_URL}/update_parameters",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {data['status']}")
            print(f"📝 Message: {data['message']}")
            print(f"🕒 Timestamp: {data['timestamp']}")
            
            print(f"\n📊 Updated Parameters:")
            for key, value in data['updated_parameters'].items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_server_connectivity():
    """ทดสอบการเชื่อมต่อ Server"""
    print(f"\n🌐 Testing Server Connectivity")
    print(f"=" * 50)
    
    try:
        # ทดสอบด้วย endpoint หลัก
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"✅ Server is running at {BASE_URL}")
        return True
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to server at {BASE_URL}")
        print(f"💡 Make sure WebRequest_Server_06.py is running")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def run_all_tests():
    """รันการทดสอบทั้งหมด"""
    print(f"🧪 Parameter Management Endpoints Test Suite")
    print(f"=" * 60)
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Target server: {BASE_URL}")
    
    # ทดสอบการเชื่อมต่อก่อน
    if not test_server_connectivity():
        print(f"\n❌ Cannot proceed with tests - server not accessible")
        return
    
    # รันการทดสอบทั้งหมด
    test_get_parameters()
    time.sleep(1)
    
    test_parameter_status()
    time.sleep(1)
    
    test_update_parameters()
    time.sleep(1)
    
    # ทดสอบการดึงพารามิเตอร์อีกครั้งหลังจากอัปเดต
    print(f"\n🔄 Testing parameters after update...")
    test_get_parameters()
    
    print(f"\n✅ All tests completed!")
    print(f"🕒 Test finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def interactive_test():
    """โหมดทดสอบแบบ Interactive"""
    while True:
        print(f"\n🧪 Parameter Management Test Menu")
        print(f"=" * 40)
        print(f"1. 🔍 Get Current Parameters")
        print(f"2. 📊 Get Parameter Status")
        print(f"3. 🔄 Update Parameters")
        print(f"4. 🌐 Test Server Connectivity")
        print(f"5. 🚀 Run All Tests")
        print(f"6. 🚪 Exit")
        
        choice = input(f"\nSelect option (1-6): ").strip()
        
        if choice == '1':
            test_get_parameters()
        elif choice == '2':
            test_parameter_status()
        elif choice == '3':
            test_update_parameters()
        elif choice == '4':
            test_server_connectivity()
        elif choice == '5':
            run_all_tests()
        elif choice == '6':
            print(f"👋 Goodbye!")
            break
        else:
            print(f"❌ Invalid option. Please select 1-6.")

if __name__ == "__main__":
    print(f"🧪 Parameter Management Endpoints Tester")
    print(f"=" * 50)
    
    mode = input(f"Select mode:\n1. 🚀 Run all tests\n2. 🎮 Interactive mode\nChoice (1-2): ").strip()
    
    if mode == '1':
        run_all_tests()
    elif mode == '2':
        interactive_test()
    else:
        print(f"❌ Invalid choice. Running all tests by default.")
        run_all_tests()
