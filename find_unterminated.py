import io
s = io.open(r'd:\test_gold\LightGBM_11_2.py', encoding='utf-8').read()
positions = []
for token in ('"""', "'''"):
    i = 0
    while True:
        j = s.find(token, i)
        if j==-1:
            break
        positions.append((j, token))
        i = j+3
positions.sort()
stack = []
for pos, token in positions:
    if not stack or stack[-1][1]!=token:
        stack.append((pos, token))
    else:
        stack.pop()
if stack:
    print('Unmatched at', stack[-1])
else:
    print('All matched')
