import pandas as pd
import numpy as np
import os
import datetime
import logging
from typing import List, Dict, Tuple, Optional, Union

# ตั้งค่า logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("Logs/enhanced_feature_creation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("EnhancedFeatureCreation")

class EnhancedFeatureCreator:
    """
    คลาสสำหรับสร้าง features และ targets ที่ปรับปรุงแล้ว
    รวมถึงการสร้าง Next_Close สำหรับหลายช่วงเวลา (สั้น-กลาง-ยาว)
    """
    
    def __init__(self, 
                 short_horizon: int = 5, 
                 medium_horizon: int = 10, 
                 long_horizon: int = 15,
                 use_percentiles: bool = True,
                 buy_percentile: float = 0.6,
                 sell_percentile: float = 0.4):
        """
        กำหนดค่าเริ่มต้นสำหรับการสร้าง features
        
        Args:
            short_horizon: จำนวนแท่งสำหรับช่วงเวลาสั้น
            medium_horizon: จำนวนแท่งสำหรับช่วงเวลากลาง
            long_horizon: จำนวนแท่งสำหรับช่วงเวลายาว
            use_percentiles: ใช้ percentiles แทนค่าคงที่สำหรับ thresholds
            buy_percentile: percentile สำหรับ buy threshold (0.6 = 60%)
            sell_percentile: percentile สำหรับ sell threshold (0.4 = 40%)
        """
        self.short_horizon = short_horizon
        self.medium_horizon = medium_horizon
        self.long_horizon = long_horizon
        self.horizons = [short_horizon, medium_horizon, long_horizon]
        
        self.use_percentiles = use_percentiles
        self.buy_percentile = buy_percentile
        self.sell_percentile = sell_percentile
        
        logger.info(f"EnhancedFeatureCreator initialized with horizons: {self.horizons}")
    
    def create_base_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        สร้าง features พื้นฐานสำหรับการวิเคราะห์
        
        Args:
            df: DataFrame ที่มีข้อมูล OHLCV
            
        Returns:
            DataFrame ที่มี features พื้นฐาน
        """
        logger.info("Creating base features")
        
        df = df.copy()
        
        # ตรวจสอบว่ามีคอลัมน์ที่จำเป็นหรือไม่
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.warning(f"Missing required columns: {missing_cols}")
            return df
        
        # แปลงคอลัมน์เป็นตัวเลข
        for col in required_cols:
            if not pd.api.types.is_numeric_dtype(df[col]):
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # --- สร้าง Technical Indicators ---
        
        # 1. Moving Averages
        for window in [5, 10, 20, 50, 100, 200]:
            df[f'MA_{window}'] = df['Close'].rolling(window=window, min_periods=1).mean()
            
        # 2. EMA
        for window in [5, 10, 20, 50, 100, 200]:
            df[f'EMA_{window}'] = df['Close'].ewm(span=window, adjust=False).mean()
        
        # 3. RSI
        def calculate_rsi(data, window=14):
            delta = data.diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            avg_gain = gain.rolling(window=window, min_periods=1).mean()
            avg_loss = loss.rolling(window=window, min_periods=1).mean()
            
            rs = avg_gain / (avg_loss + 1e-10)  # เพิ่ม epsilon เพื่อป้องกันการหารด้วย 0
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        df['RSI14'] = calculate_rsi(df['Close'], 14)
        
        # 4. Bollinger Bands
        for window in [20]:
            df[f'BB_Middle_{window}'] = df['Close'].rolling(window=window, min_periods=1).mean()
            df[f'BB_Std_{window}'] = df['Close'].rolling(window=window, min_periods=1).std()
            df[f'Upper_BB_{window}'] = df[f'BB_Middle_{window}'] + (df[f'BB_Std_{window}'] * 2)
            df[f'Lower_BB_{window}'] = df[f'BB_Middle_{window}'] - (df[f'BB_Std_{window}'] * 2)
            df[f'BB_Width_{window}'] = (df[f'Upper_BB_{window}'] - df[f'Lower_BB_{window}']) / df[f'BB_Middle_{window}']
        
        # 5. MACD
        df['EMA_12'] = df['Close'].ewm(span=12, adjust=False).mean()
        df['EMA_26'] = df['Close'].ewm(span=26, adjust=False).mean()
        df['MACD'] = df['EMA_12'] - df['EMA_26']
        df['MACD_Signal'] = df['MACD'].ewm(span=9, adjust=False).mean()
        df['MACD_Hist'] = df['MACD'] - df['MACD_Signal']
        
        # 6. Market Condition Indicators
        df['Above_EMA200'] = (df['Close'] > df['EMA_200']).astype(int)
        df['EMA_50_200_Diff'] = ((df['EMA_50'] - df['EMA_200']) / df['EMA_200']) * 100
        
        # 7. Volatility Indicators
        df['ATR_14'] = self._calculate_atr(df, 14)
        
        # 8. Volume Indicators
        df['Volume_MA_20'] = df['Volume'].rolling(window=20, min_periods=1).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_MA_20']
        
        logger.info(f"Created {len(df.columns) - len(required_cols)} base features")
        return df
    
    def _calculate_atr(self, df: pd.DataFrame, window: int = 14) -> pd.Series:
        """คำนวณ Average True Range (ATR)"""
        high_low = df['High'] - df['Low']
        high_close = (df['High'] - df['Close'].shift()).abs()
        low_close = (df['Low'] - df['Close'].shift()).abs()
        
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(window=window, min_periods=1).mean()
        
        return atr
    
    def create_next_close_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        สร้าง Next_Close features สำหรับหลายช่วงเวลา (สั้น-กลาง-ยาว)
        
        Args:
            df: DataFrame ที่มีข้อมูล OHLCV
            
        Returns:
            DataFrame ที่มี Next_Close features
        """
        logger.info(f"Creating Next_Close features for horizons: {self.horizons}")
        
        df = df.copy()
        
        # สร้าง Next_Close สำหรับแต่ละ horizon
        for horizon in self.horizons:
            df[f'Next_Close_{horizon}'] = df['Close'].shift(-horizon)
            df[f'Next_High_{horizon}'] = df['High'].shift(-horizon)
            df[f'Next_Low_{horizon}'] = df['Low'].shift(-horizon)
            
            # คำนวณการเปลี่ยนแปลงของราคา
            df[f'Price_Change_{horizon}'] = (df[f'Next_Close_{horizon}'] - df['Close']) / df['Close']
            df[f'Price_Change_Pct_{horizon}'] = df[f'Price_Change_{horizon}'] * 100
        
        logger.info(f"Created Next_Close features for {len(self.horizons)} horizons")
        return df
    
    def create_targets(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        สร้าง Target_Buy และ Target_Sell สำหรับหลายช่วงเวลา
        
        Args:
            df: DataFrame ที่มี Next_Close features
            
        Returns:
            DataFrame ที่มี Target_Buy และ Target_Sell
        """
        logger.info("Creating targets for multiple horizons")
        
        df = df.copy()
        
        for horizon in self.horizons:
            # ตรวจสอบว่ามีคอลัมน์ที่จำเป็นหรือไม่
            required_cols = [f'Next_Close_{horizon}', f'Price_Change_Pct_{horizon}']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.warning(f"Missing required columns for horizon {horizon}: {missing_cols}")
                continue
            
            # กำหนด thresholds
            if self.use_percentiles:
                buy_threshold = df[f'Price_Change_Pct_{horizon}'].quantile(self.buy_percentile)
                sell_threshold = df[f'Price_Change_Pct_{horizon}'].quantile(self.sell_percentile)
            else:
                # ค่าคงที่ (สามารถปรับได้)
                buy_threshold = 0.1  # 0.1%
                sell_threshold = -0.1  # -0.1%
            
            logger.info(f"Horizon {horizon} - Buy threshold: {buy_threshold:.4f}%, Sell threshold: {sell_threshold:.4f}%")
            
            # สร้าง Target_Buy และ Target_Sell
            df[f'Target_Buy_{horizon}'] = (df[f'Price_Change_Pct_{horizon}'] > buy_threshold).astype(int)
            df[f'Target_Sell_{horizon}'] = (df[f'Price_Change_Pct_{horizon}'] < sell_threshold).astype(int)
            
            # สร้าง Target หลัก (รวม Buy และ Sell)
            df[f'Target_{horizon}'] = ((df[f'Target_Buy_{horizon}'] == 1) | (df[f'Target_Sell_{horizon}'] == 1)).astype(int)
        
        logger.info(f"Created targets for {len(self.horizons)} horizons")
        return df
    
    def create_market_scenario_data(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        สร้างข้อมูลตามสภาวะตลาดทั้ง 6 สถานการณ์
        
        Args:
            df: DataFrame ที่มี features และ targets
            
        Returns:
            Dict ของ DataFrames แยกตามสถานการณ์
        """
        logger.info("Creating market scenario data")
        
        df = df.copy()
        
        # ตรวจสอบว่ามีคอลัมน์ที่จำเป็นหรือไม่
        if 'Above_EMA200' not in df.columns:
            logger.warning("Missing 'Above_EMA200' column, creating it")
            if 'EMA_200' in df.columns and 'Close' in df.columns:
                df['Above_EMA200'] = (df['Close'] > df['EMA_200']).astype(int)
            else:
                logger.error("Cannot create 'Above_EMA200', missing required columns")
                return {}
        
        # แยกข้อมูลตามสภาวะตลาด
        scenarios = {}
        
        # 1. Trend Following (ทั้งหมด)
        scenarios['trend_following'] = df.copy()
        
        # 2. Trend Following - Buy (เมื่อราคา > EMA200)
        scenarios['trend_following_Buy'] = df[df['Above_EMA200'] == 1].copy()
        
        # 3. Trend Following - Sell (เมื่อราคา < EMA200)
        scenarios['trend_following_Sell'] = df[df['Above_EMA200'] == 0].copy()
        
        # 4. Counter Trend (ทั้งหมด)
        scenarios['counter_trend'] = df.copy()
        
        # 5. Counter Trend - Buy (เมื่อราคา < EMA200)
        scenarios['counter_trend_Buy'] = df[df['Above_EMA200'] == 0].copy()
        
        # 6. Counter Trend - Sell (เมื่อราคา > EMA200)
        scenarios['counter_trend_Sell'] = df[df['Above_EMA200'] == 1].copy()
        
        # แสดงจำนวนข้อมูลในแต่ละสถานการณ์
        for scenario, scenario_df in scenarios.items():
            logger.info(f"Scenario '{scenario}': {len(scenario_df)} rows")
        
        return scenarios
    
    def process_full_pipeline(self, df: pd.DataFrame, remove_future_data: bool = True) -> Tuple[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        ประมวลผลข้อมูลทั้งหมดในขั้นตอนเดียว
        
        Args:
            df: DataFrame ที่มีข้อมูล OHLCV
            remove_future_data: ลบข้อมูลในอนาคตหรือไม่ (Next_Close, etc.)
            
        Returns:
            Tuple ของ (DataFrame ที่มี features และ targets, Dict ของ DataFrames แยกตามสถานการณ์)
        """
        logger.info("Starting full pipeline processing")
        
        # 1. สร้าง features พื้นฐาน
        df = self.create_base_features(df)
        
        # 2. สร้าง Next_Close features
        df = self.create_next_close_features(df)
        
        # 3. สร้าง targets
        df = self.create_targets(df)
        
        # 4. สร้างข้อมูลตามสภาวะตลาด
        scenarios = self.create_market_scenario_data(df)
        
        # 5. ลบข้อมูลในอนาคต (ถ้าต้องการ)
        if remove_future_data:
            future_cols = []
            for horizon in self.horizons:
                future_cols.extend([
                    f'Next_Close_{horizon}',
                    f'Next_High_{horizon}',
                    f'Next_Low_{horizon}'
                ])
            
            # ลบข้อมูลในอนาคตออกจาก df หลัก
            df_clean = df.drop(columns=future_cols, errors='ignore')
            
            # ลบข้อมูลในอนาคตออกจาก scenarios
            for scenario in scenarios:
                scenarios[scenario] = scenarios[scenario].drop(columns=future_cols, errors='ignore')
            
            logger.info(f"Removed {len(future_cols)} future data columns")
            
            return df_clean, scenarios
        
        return df, scenarios
    
    def save_processed_data(self, df: pd.DataFrame, scenarios: Dict[str, pd.DataFrame], 
                           symbol: str, timeframe: str, output_dir: str = "LightGBM/Data_Processed") -> None:
        """
        บันทึกข้อมูลที่ประมวลผลแล้ว
        
        Args:
            df: DataFrame ที่มี features และ targets
            scenarios: Dict ของ DataFrames แยกตามสถานการณ์
            symbol: สัญลักษณ์
            timeframe: ไทม์เฟรม
            output_dir: ไดเรกทอรีสำหรับบันทึกไฟล์
        """
        # สร้างไดเรกทอรีถ้ายังไม่มี
        os.makedirs(output_dir, exist_ok=True)
        
        # บันทึกไฟล์หลัก
        main_file = os.path.join(output_dir, f"{timeframe}_{symbol}_full_features.csv")
        df.to_csv(main_file, index=False)
        logger.info(f"Saved main file to: {main_file}")
        
        # บันทึกไฟล์สำหรับแต่ละสถานการณ์
        for scenario, scenario_df in scenarios.items():
            scenario_file = os.path.join(output_dir, f"{timeframe}_{symbol}_{scenario}.csv")
            scenario_df.to_csv(scenario_file, index=False)
            logger.info(f"Saved scenario '{scenario}' to: {scenario_file}")

# ฟังก์ชันสำหรับใช้งานง่าย
def process_data_with_enhanced_features(df: pd.DataFrame, symbol: str, timeframe: str,
                                       short_horizon: int = 5, 
                                       medium_horizon: int = 10, 
                                       long_horizon: int = 15,
                                       save_output: bool = True,
                                       output_dir: str = "LightGBM/Data_Processed") -> Tuple[pd.DataFrame, Dict[str, pd.DataFrame]]:
    """
    ฟังก์ชันสำหรับประมวลผลข้อมูลด้วย EnhancedFeatureCreator
    
    Args:
        df: DataFrame ที่มีข้อมูล OHLCV
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
        short_horizon: จำนวนแท่งสำหรับช่วงเวลาสั้น
        medium_horizon: จำนวนแท่งสำหรับช่วงเวลากลาง
        long_horizon: จำนวนแท่งสำหรับช่วงเวลายาว
        save_output: บันทึกผลลัพธ์หรือไม่
        output_dir: ไดเรกทอรีสำหรับบันทึกไฟล์
        
    Returns:
        Tuple ของ (DataFrame ที่มี features และ targets, Dict ของ DataFrames แยกตามสถานการณ์)
    """
    # สร้าง EnhancedFeatureCreator
    creator = EnhancedFeatureCreator(
        short_horizon=short_horizon,
        medium_horizon=medium_horizon,
        long_horizon=long_horizon
    )
    
    # ประมวลผลข้อมูล
    df_processed, scenarios = creator.process_full_pipeline(df)
    
    # บันทึกผลลัพธ์ (ถ้าต้องการ)
    if save_output:
        creator.save_processed_data(df_processed, scenarios, symbol, timeframe, output_dir)
    
    return df_processed, scenarios

# ฟังก์ชันสำหรับปรับปรุง process_trade_targets
def improved_process_trade_targets(df: pd.DataFrame, symbol: str, timeframe: str, 
                                  horizon: int = 5) -> pd.DataFrame:
    """
    ปรับปรุงฟังก์ชัน process_trade_targets เพื่อแก้ไขปัญหาข้อมูลไม่ต่อเนื่อง
    
    Args:
        df: DataFrame ที่มีข้อมูล trade
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
        horizon: จำนวนแท่งสำหรับ Next_Close
        
    Returns:
        DataFrame ที่มี Target
    """
    print(f"\n🏗️ เปิดใช้งาน improved_process_trade_targets")
    
    if df.empty or len(df) < 10:
        print("⚠️ ไม่มีข้อมูล เพียงพอสำหรับสร้าง Target")
        return df
    
    try:
        # ตรวจสอบคอลัมน์ที่จำเป็น
        required_cols = ['Profit', 'Exit Condition', 'Risk', 'Reward', 'Trade Type']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"⚠️ ไม่พบ คอลัมน์ที่จำเป็น: {missing_cols}")
            return df
        
        # แปลงคอลัมน์ Profit เป็นตัวเลข
        if not pd.api.types.is_numeric_dtype(df["Profit"]):
            df["Profit"] = pd.to_numeric(df["Profit"], errors='coerce')
            df = df.dropna(subset=["Profit"])
        
        # ตรวจสอบว่ามีคอลัมน์ Next_Close หรือไม่
        next_close_col = f'Next_Close_{horizon}'
        if next_close_col not in df.columns:
            print(f"⚠️ ไม่พบคอลัมน์ {next_close_col} ในข้อมูล")
            print("🔄 สร้างคอลัมน์ Next_Close จากข้อมูลต้นฉบับ...")
            
            # ในกรณีนี้ ควรใช้ข้อมูลต้นฉบับที่มีการสร้าง Next_Close ไว้แล้ว
            # และใช้ merge เพื่อเพิ่มคอลัมน์ Next_Close เข้าไปใน df
            
            # ตัวอย่าง (ต้องปรับให้เข้ากับโครงสร้างข้อมูลจริง)
            # original_data = load_original_data(symbol, timeframe)
            # original_data[next_close_col] = original_data['Close'].shift(-horizon)
            # df = pd.merge_asof(df, original_data[[df.index.name, next_close_col]], 
            #                    left_index=True, right_index=True)
        
        # ======================================================
        # วิธีที่ 1: สร้าง Target จาก Risk/Reward Ratio
        # ======================================================
        df['RR_Ratio'] = df['Reward'] / df['Risk']
        
        # ======================================================
        # วิธีที่ 2: สร้าง Target จาก Profit
        # ======================================================
        
        # คำนวณ thresholds จาก percentiles
        buy_threshold = df["Profit"].quantile(0.6)
        sell_threshold = df["Profit"].quantile(0.4)
        
        print(f"📊 Target Thresholds: Buy={buy_threshold:.4f}%, Sell={sell_threshold:.4f}%")
        
        # ตรวจสอบว่า Profit column มีค่าที่ถูกต้อง
        valid_profit_count = df["Profit"].notna().sum()
        total_profit_count = len(df)
        print(f"📊 Profit column status: {valid_profit_count}/{total_profit_count} valid values")
        
        # สร้าง Target_Buy และ Target_Sell
        df['Target_Buy'] = (df['Profit'] > buy_threshold).astype(int)
        df['Target_Sell'] = (df['Profit'] < sell_threshold).astype(int)
        
        # สร้าง Target หลัก (รวม Buy และ Sell)
        df['Target'] = ((df['Target_Buy'] == 1) | (df['Target_Sell'] == 1)).astype(int)
        
        # ======================================================
        # วิธีที่ 3: สร้าง Multi-class Target
        # ======================================================
        print(f"\n🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)")
        
        # กำหนดเงื่อนไขสำหรับแต่ละ class
        conditions = [
            # Class 4: Strong Buy (Buy trade, large profit)
            (df['Trade Type'] == 'Buy') & (df['Profit'] > df['Profit'].quantile(0.8)),
            
            # Class 3: Weak Buy (Buy trade, small profit/breakeven)
            (df['Trade Type'] == 'Buy') & (df['Profit'] > 0) & (df['Profit'] <= df['Profit'].quantile(0.8)),
            
            # Class 2: No Trade/Loss (Any trade, loss)
            (df['Profit'] <= 0),
            
            # Class 1: Weak Sell (Sell trade, small profit/breakeven)
            (df['Trade Type'] == 'Sell') & (df['Profit'] > 0) & (df['Profit'] <= df['Profit'].quantile(0.8)),
            
            # Class 0: Strong Sell (Sell trade, large profit)
            (df['Trade Type'] == 'Sell') & (df['Profit'] > df['Profit'].quantile(0.8))
        ]
        
        # กำหนดค่าสำหรับแต่ละ class
        class_values = [4, 3, 2, 1, 0]
        
        # สร้าง Target_Multiclass
        df['Target_Multiclass'] = np.select(conditions, class_values, default=2)
        
        # แสดงสถิติของ Target_Multiclass
        print(f"📊 Multi-class Target Statistics:")
        class_names = {0: 'strong_sell', 1: 'weak_sell', 2: 'no_trade', 3: 'weak_buy', 4: 'strong_buy'}
        
        for class_id, count in df['Target_Multiclass'].value_counts().sort_index().items():
            class_name = class_names.get(class_id, f'unknown_{class_id}')
            percentage = (count / len(df)) * 100
            print(f"  Class {class_id} ({class_name}): {count} samples ({percentage:.1f}%)")
        
        # ตรวจสอบว่ามีอย่างน้อย 2 classes และแต่ละ class มีข้อมูลเพียงพอ
        min_samples_per_class = 10
        unique = df['Target_Multiclass'].unique()
        counts = [len(df[df['Target_Multiclass'] == u]) for u in unique]
        
        valid_classes = [class_id for class_id, count in zip(unique, counts) if count >= min_samples_per_class]
        
        if len(valid_classes) < 2:
            print(f"⚠️ Warning: มีเพียง {len(valid_classes)} classes ที่มีข้อมูลเพียงพอ (>= {min_samples_per_class} samples)")
            print(f"⚠️ จะใช้ Binary Classification แทน Multi-class")
            # แปลงเป็น binary: 0-2 = 0 (sell\no_trade), 3-4 = 1 (buy)
            df['Target_Multiclass'] = np.where(df['Target_Multiclass'] >= 3, 1, 0)
        else:
            print(f"✅ Multi-class Target ถูกต้อง: {len(valid_classes)} classes พร้อมใช้งาน")
        
        # แสดงการกระจายของ Target_Multiclass
        print(f"\n📊 Multi-class Target Distribution:")
        print(df['Target_Multiclass'].value_counts())
        
        # แสดงช่วง Profit ของแต่ละ class
        for class_id in sorted(df['Target_Multiclass'].unique()):
            class_df = df[df['Target_Multiclass'] == class_id]
            class_name = class_names.get(class_id, f'unknown_{class_id}')
            min_profit = class_df['Profit'].min()
            max_profit = class_df['Profit'].max()
            print(f"Class {class_id} ({class_name}): {len(class_df)} trades, Profit range: {min_profit:.1f} to {max_profit:.1f}")
        
        return df
    
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดใน process_trade_targets: {str(e)}")
        import traceback
        traceback.print_exc()
        return df