ใช้ภาษาไทยได้หรือไม่ ถ้าได้ใช้ภาษาไทย
ช่วยอ่านไฟล์ log : LightGBM\Log_Train.txt และวิเคราะห์ หาแนวทางการแก้ไข และพัฒนาให้ดีขึ้น

// ช่วยปรับ code : LightGBM_Parameter_Stability_02.py
ให้มีการตรวจสอบเพื่อใช้งานกับ LightGBM_11_4.py
จากการเพิ่มเติมระบบใหม่ของ Next Close System Configuration (ระบบใหม่)
เพื่อปรับค่าใหม่เหมาะสม และตรวจสอบขั้นตอนการแนะนำการแปรับค่าให้ละเอียดมากขึ้น
โดยอาจเพิ่มคำแนะนำ และเหตุผล 
เพื่อให้ผู้ใช้คนใหม่เข้าใจการปรับค่าที่ง่าย

// ช่วยตรวจสอบขั้นตอน selected_features เนื่องจากมีการเลือกที่น้อยมาก (ช่วยแนะนำอีกครั้งว่าแบบนี้ดีแล้ว หรือควรปรับปรุง ให้มีการเลือกที่มากขึ้น)
และเพิ่มไฟล์ .txt สำหรับ must_have_features เพื่อไว้ตรวจสอบง่ายขึ้น
LightGBM/Multi
├─ feature_importance
│   └─ {timeframe}_must_have_features.pkl
├─ feature_selected
│   ├─ {symbol}_{timeframe}_selected_features.pkl >> ตัวอย่างชื่อไฟล์ USDJPY_M60_selected_features
│   └─ {symbol}_{timeframe}_selected_features.txt

// มี 'Entry Day', 'Entry Hour แต่ไม่พบ สถิ ช่วยตรวจสอบ
+ ⚠️ ไม่พบ สถิติรายวัน - ไม่มีคอลัมน์ DayOfWeek หรือข้อมูลว่างเปล่า
+ ⚠️ ไม่พบ สถิติรายชั่วโมง - ไม่มีคอลัมน์ Hour หรือข้อมูลว่างเปล่า
แต่ขั้นตอน analyze sl tp performance มีสถิติรายวัน และรายชั่วโมง
// log
📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                48.50
Expectancy          1.93
📈 สถิติสำหรับ Sell Trades:
Win%                46.12
Expectancy          -54.45
📈 สถิติสำหรับ Buy_sell Trades:
Win%                47.33
Expectancy          -25.87
========================================
🔍 Debug: trade_df columns = ['Entry Day', 'Entry Hour', 'Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Profit', 'Trade Type', 'Exit Condition', 'Scenario', 'Horizon', 'Horizon_Name', 'Strategy', 'Market_Type']
🔍 Debug: trade_df shape = (214209, 14)
⚠️ ไม่พบ สถิติรายวัน - ไม่มีคอลัมน์ DayOfWeek หรือข้อมูลว่างเปล่า
⚠️ ไม่พบ สถิติรายชั่วโมง - ไม่มีคอลัมน์ Hour หรือข้อมูลว่างเปล่า
========================================

// ค่าสถิติเป็น 0.00
// log
 เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน
--------------------------------------------------
TP Hit              0         0.00%
SL Hit              0         0.00%
Technical Exit      0         0.00%
SL + Tech Exit      0         0.00%
==================================================

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                        Main_Target
           count        mean        sum        mean
DayOfWeek
Monday     42426  -18.696200  -793205.0    0.474355
Tuesday    43320  -23.860549 -1033639.0    0.470845
Wednesday  43134  -37.661659 -1624498.0    0.471739
Thursday   43149  -25.285128 -1091028.0    0.474240
Friday     42174  -23.451368  -989038.0    0.473467
Saturday       6 -953.166667    -5719.0    0.500000

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                        Main_Target
      count        mean        sum        mean
Hour
0      3984  -61.637299  -245563.0    0.458333
1      9192   -5.648716   -51923.0    0.477916
2      9201  -25.254320  -232365.0    0.469949
3      9198  -21.280387  -195737.0    0.480865
4      9201    1.372025    12624.0    0.489838
5      9174   13.161325   120742.0    0.499346
6      9177   -8.415604   -77230.0    0.481748
7      9192  -24.311902  -223475.0    0.479003
8      9183   -3.633235   -33364.0    0.478384
9      9237    8.700877    80370.0    0.497564
10     9216  -15.175781  -139860.0    0.482639
11     9228  -29.956003  -276434.0    0.478544
12     9228  -14.634915  -135051.0    0.481361
13     9222  -32.244307  -297357.0    0.485795
14     9231  -28.512187  -263196.0    0.472647
15     9240  -10.398918   -96086.0    0.476948
16     9228  -27.813502  -256663.0    0.481795
17     9204  -26.157323  -240752.0    0.459148
18     9219  -34.263261  -315873.0    0.462523
19     9177  -49.765719  -456700.0    0.457666
20     9030  -12.199889  -110165.0    0.464784
21     9000  -21.263444  -191371.0    0.455222
22     8907  -46.701695  -415972.0    0.466375
23     8340 -179.343645 -1495726.0    0.395324
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM/Multi/results\M60_GOLD_time_analysis.png

// มี error แต่เนื่องจากที่แสดง terminal มีแค่นี้ ช่วยตรวจสอบจาก Log_Train.txt เพื่อให้มีการแก้ไขที่ครบ

Traceback (most recent call last):
  File "d:\test_gold\LightGBM_11_4.py", line 18323, in main
    'training_date': datetime.now().isoformat(),
                     ^^^^^^^^
UnboundLocalError: cannot access local variable 'datetime' where it is not associated with a value