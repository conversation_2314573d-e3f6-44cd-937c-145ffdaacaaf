การทดสอบเป็นการทดสอบตัวอย่างบางส่วน ได้ผลที่ต่างกัน
ช่วยหาสาเหตุ และวิเคราะห์

ช่วยอ่านไฟล์ log : LightGBM\Log_Train.txt และวิเคราะห์ หาแนวทางการแก้ไข และพัฒนาให้ดีขึ้น
เป็นการทดสอบของ "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv"

1. ทดสอบ "CSV_Files_Fixed/GOLD_H1_FIXED.csv" ได้โมเดลการเทรนครบทั้ง 6 โมเดล
counter_trend, counter_trend_Buy, counter_trend_Sell
trend_following, trend_following_Buy, trend_following_Sell

โครงสร้างไฟล์ ที่ได้ "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
LightGBM/Multi
├─ models
│   ├─ counter_trend
│   │   ├─ M60_GOLD_features.pkl
│   │   ├─ M60_GOLD_scaler.pkl
│   │   └─ M60_GOLD_trained.pkl
│   ├─ counter_trend_Buy
│   │   ├─ M60_GOLD_features.pkl
│   │   ├─ M60_GOLD_scaler.pkl
│   │   └─ M60_GOLD_trained.pkl
│   ├─ counter_trend_Sell
│   │   ├─ M60_GOLD_features.pkl
│   │   ├─ M60_GOLD_scaler.pkl
│   │   └─ M60_GOLD_trained.pkl
│   ├─ trend_following
│   │   ├─ M60_GOLD_features.pkl
│   │   ├─ M60_GOLD_scaler.pkl
│   │   └─ M60_GOLD_trained.pkl
│   ├─ trend_following_Buy
│   │   ├─ M60_GOLD_features.pkl
│   │   ├─ M60_GOLD_scaler.pkl
│   │   └─ M60_GOLD_trained.pkl
│   ├─ trend_following_Sell
│   │   ├─ M60_GOLD_features.pkl
│   │   ├─ M60_GOLD_scaler.pkl
│   │   └─ M60_GOLD_trained.pkl

2. ทดสอบ "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv" ได้โมเดลการเทรนครบทั้ง 2 โมเดล
counter_trend, trend_following
เหมือนการทดสอบไม่ผ่านช่วงไหน หรือว่ามีการกรองอะไรที่เฉพาะทำให้หยุดทำงาน 
ทำให้ไม่ผ่านการเทรนโมเดลครบทั้ง 6 โมเดล

โครงสร้างไฟล์ ที่ได้ "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv"
LightGBM/Multi
├─ models
│   ├─ M60_GBPUSD
│   │   ├─ M60_GOLD_features.pkl
│   │   ├─ M60_GOLD_scaler.pkl
│   │   └─ M60_GOLD_trained.pkl

ทั้ง 2 มีการเทรนที่ไม่เหมือนกัน ต้องการให้เป็นแบบ "CSV_Files_Fixed/GOLD_H1_FIXED.csv" หรือเทรนครบ 6 โมเดล
