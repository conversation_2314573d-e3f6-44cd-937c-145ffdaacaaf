import pandas as pd
import numpy as np
import os
import logging
from typing import List, Dict, Tuple, Optional, Union
import lightgbm as lgb
import joblib
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

# ตั้งค่า logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("Logs/multi_timeframe_decision.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("MultiTimeframeDecision")

class MultiTimeframeDecisionSystem:
    """
    ระบบการตัดสินใจจากการวิเคราะห์หลายช่วงเวลา (สั้น-กลาง-ยาว)
    """
    
    def __init__(self, 
                 short_horizon: int = 5, 
                 medium_horizon: int = 10, 
                 long_horizon: int = 15,
                 models_dir: str = "LightGBM/Multi/models",
                 thresholds_dir: str = "LightGBM/Multi/thresholds"):
        """
        กำหนดค่าเริ่มต้นสำหรับระบบการตัดสินใจ
        
        Args:
            short_horizon: จำนวนแท่งสำหรับช่วงเวลาสั้น
            medium_horizon: จำนวนแท่งสำหรับช่วงเวลากลาง
            long_horizon: จำนวนแท่งสำหรับช่วงเวลายาว
            models_dir: ไดเรกทอรีสำหรับโมเดล
            thresholds_dir: ไดเรกทอรีสำหรับ thresholds
        """
        self.short_horizon = short_horizon
        self.medium_horizon = medium_horizon
        self.long_horizon = long_horizon
        self.horizons = [short_horizon, medium_horizon, long_horizon]
        self.horizon_names = {
            short_horizon: "short",
            medium_horizon: "medium",
            long_horizon: "long"
        }
        
        self.models_dir = models_dir
        self.thresholds_dir = thresholds_dir
        
        # สร้างไดเรกทอรีถ้ายังไม่มี
        os.makedirs(models_dir, exist_ok=True)
        os.makedirs(thresholds_dir, exist_ok=True)
        
        # เก็บโมเดลและ thresholds
        self.models = {}
        self.thresholds = {}
        
        logger.info(f"MultiTimeframeDecisionSystem initialized with horizons: {self.horizons}")
    
    def train_models(self, 
                    df_dict: Dict[str, pd.DataFrame], 
                    symbol: str, 
                    timeframe: str,
                    scenarios: List[str] = ["trend_following", "counter_trend"],
                    target_types: List[str] = ["Buy", "Sell"],
                    test_size: float = 0.2,
                    random_state: int = 42) -> Dict:
        """
        เทรนโมเดลสำหรับแต่ละช่วงเวลาและสถานการณ์
        
        Args:
            df_dict: Dict ของ DataFrames แยกตามสถานการณ์
            symbol: สัญลักษณ์
            timeframe: ไทม์เฟรม
            scenarios: รายการสถานการณ์ที่ต้องการเทรน
            target_types: รายการประเภทเป้าหมาย (Buy, Sell)
            test_size: สัดส่วนข้อมูลทดสอบ
            random_state: random seed
            
        Returns:
            Dict ของผลลัพธ์การเทรน
        """
        from sklearn.model_selection import train_test_split
        
        results = {}
        
        for scenario in scenarios:
            if scenario not in df_dict:
                logger.warning(f"Scenario '{scenario}' not found in df_dict")
                continue
            
            scenario_df = df_dict[scenario]
            
            for horizon in self.horizons:
                horizon_name = self.horizon_names[horizon]
                
                for target_type in target_types:
                    target_col = f'Target_{target_type}_{horizon}'
                    
                    if target_col not in scenario_df.columns:
                        logger.warning(f"Target column '{target_col}' not found in scenario '{scenario}'")
                        continue
                    
                    logger.info(f"Training model for {symbol} {timeframe} - {scenario} - {horizon_name} - {target_type}")
                    
                    # กำหนดคอลัมน์ที่ไม่ใช่ features
                    drop_cols = []
                    
                    # ลบคอลัมน์ที่เกี่ยวข้องกับ targets และข้อมูลในอนาคต
                    for h in self.horizons:
                        drop_cols.extend([
                            f'Target_{t}_{h}' for t in target_types
                        ])
                        drop_cols.extend([
                            f'Target_{h}',
                            f'Next_Close_{h}',
                            f'Next_High_{h}',
                            f'Next_Low_{h}',
                            f'Price_Change_{h}',
                            f'Price_Change_Pct_{h}'
                        ])
                    
                    # ลบคอลัมน์อื่นๆ ที่ไม่ใช่ features
                    drop_cols.extend(['DateTime', 'Open', 'High', 'Low', 'Close', 'Volume'])
                    
                    # สร้าง features และ target
                    X = scenario_df.drop(columns=drop_cols, errors='ignore')
                    y = scenario_df[target_col]
                    
                    # แบ่งข้อมูลสำหรับเทรนและทดสอบ
                    X_train, X_test, y_train, y_test = train_test_split(
                        X, y, test_size=test_size, random_state=random_state, shuffle=False
                    )
                    
                    # เทรนโมเดล
                    model = lgb.LGBMClassifier(
                        n_estimators=100,
                        learning_rate=0.05,
                        max_depth=5,
                        num_leaves=31,
                        random_state=random_state
                    )
                    
                    model.fit(
                        X_train, y_train,
                        eval_set=[(X_test, y_test)],
                        eval_metric='auc',
                        early_stopping_rounds=20,
                        verbose=False
                    )
                    
                    # ทำนายผลลัพธ์
                    y_pred_proba = model.predict_proba(X_test)[:, 1]
                    
                    # หา threshold ที่ดีที่สุด
                    best_threshold, best_f1 = self._find_best_threshold(y_test, y_pred_proba)
                    
                    # ทำนายด้วย threshold ที่ดีที่สุด
                    y_pred = (y_pred_proba >= best_threshold).astype(int)
                    
                    # คำนวณ metrics
                    accuracy = accuracy_score(y_test, y_pred)
                    precision = precision_score(y_test, y_pred, zero_division=0)
                    recall = recall_score(y_test, y_pred, zero_division=0)
                    f1 = f1_score(y_test, y_pred, zero_division=0)
                    cm = confusion_matrix(y_test, y_pred)
                    
                    # บันทึกผลลัพธ์
                    model_key = f"{symbol}_{timeframe}_{scenario}_{horizon_name}_{target_type}"
                    
                    self.models[model_key] = model
                    self.thresholds[model_key] = best_threshold
                    
                    # บันทึกโมเดลและ threshold
                    model_path = os.path.join(self.models_dir, f"{model_key}.joblib")
                    threshold_path = os.path.join(self.thresholds_dir, f"{model_key}.joblib")
                    
                    joblib.dump(model, model_path)
                    joblib.dump(best_threshold, threshold_path)
                    
                    # เก็บผลลัพธ์
                    results[model_key] = {
                        'accuracy': accuracy,
                        'precision': precision,
                        'recall': recall,
                        'f1': f1,
                        'confusion_matrix': cm,
                        'best_threshold': best_threshold,
                        'feature_importance': dict(zip(X.columns, model.feature_importances_))
                    }
                    
                    logger.info(f"Model {model_key} - Accuracy: {accuracy:.4f}, F1: {f1:.4f}, Threshold: {best_threshold:.4f}")
        
        return results
    
    def _find_best_threshold(self, y_true, y_pred_proba, thresholds=None):
        """หา threshold ที่ดีที่สุดโดยใช้ F1 score"""
        if thresholds is None:
            thresholds = np.arange(0.1, 1.0, 0.05)
        
        best_threshold = 0.5
        best_f1 = 0.0
        
        for threshold in thresholds:
            y_pred = (y_pred_proba >= threshold).astype(int)
            f1 = f1_score(y_true, y_pred, zero_division=0)
            
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold
        
        return best_threshold, best_f1
    
    def load_models(self, symbol: str, timeframe: str, scenarios: List[str] = None, target_types: List[str] = None):
        """โหลดโมเดลและ thresholds จากไฟล์"""
        if scenarios is None:
            scenarios = ["trend_following", "counter_trend"]
        
        if target_types is None:
            target_types = ["Buy", "Sell"]
        
        for scenario in scenarios:
            for horizon in self.horizons:
                horizon_name = self.horizon_names[horizon]
                
                for target_type in target_types:
                    model_key = f"{symbol}_{timeframe}_{scenario}_{horizon_name}_{target_type}"
                    model_path = os.path.join(self.models_dir, f"{model_key}.joblib")
                    threshold_path = os.path.join(self.thresholds_dir, f"{model_key}.joblib")
                    
                    if os.path.exists(model_path) and os.path.exists(threshold_path):
                        self.models[model_key] = joblib.load(model_path)
                        self.thresholds[model_key] = joblib.load(threshold_path)
                        logger.info(f"Loaded model and threshold for {model_key}")
                    else:
                        logger.warning(f"Model or threshold file not found for {model_key}")
    
    def predict(self, 
               df: pd.DataFrame, 
               symbol: str, 
               timeframe: str,
               scenario: str = None) -> Dict:
        """
        ทำนายผลลัพธ์จากโมเดลหลายช่วงเวลา
        
        Args:
            df: DataFrame ที่มี features
            symbol: สัญลักษณ์
            timeframe: ไทม์เฟรม
            scenario: สถานการณ์ที่ต้องการทำนาย (ถ้าไม่ระบุ จะตรวจสอบจาก Above_EMA200)
            
        Returns:
            Dict ของผลลัพธ์การทำนาย
        """
        # ตรวจสอบว่าโหลดโมเดลแล้วหรือยัง
        if not self.models:
            logger.warning("No models loaded. Please load models first.")
            return {}
        
        # ตรวจสอบสถานการณ์
        if scenario is None:
            if 'Above_EMA200' in df.columns:
                above_ema200 = df['Above_EMA200'].iloc[-1]
                if above_ema200 == 1:
                    # ถ้าราคาอยู่เหนือ EMA200
                    trend_scenario = "trend_following"
                    counter_scenario = "counter_trend_Sell"
                else:
                    # ถ้าราคาอยู่ใต้ EMA200
                    trend_scenario = "trend_following"
                    counter_scenario = "counter_trend_Buy"
            else:
                logger.warning("Above_EMA200 column not found. Using default scenarios.")
                trend_scenario = "trend_following"
                counter_scenario = "counter_trend"
        else:
            trend_scenario = scenario
            counter_scenario = scenario
        
        # ทำนายผลลัพธ์
        predictions = {}
        
        for horizon in self.horizons:
            horizon_name = self.horizon_names[horizon]
            
            # ทำนาย Buy
            buy_model_key = f"{symbol}_{timeframe}_{trend_scenario}_{horizon_name}_Buy"
            if buy_model_key in self.models:
                buy_model = self.models[buy_model_key]
                buy_threshold = self.thresholds[buy_model_key]
                
                buy_proba = buy_model.predict_proba(df)[:, 1]
                buy_signal = (buy_proba >= buy_threshold).astype(int)
                
                predictions[f"{horizon_name}_buy_proba"] = buy_proba
                predictions[f"{horizon_name}_buy_signal"] = buy_signal
            
            # ทำนาย Sell
            sell_model_key = f"{symbol}_{timeframe}_{trend_scenario}_{horizon_name}_Sell"
            if sell_model_key in self.models:
                sell_model = self.models[sell_model_key]
                sell_threshold = self.thresholds[sell_model_key]
                
                sell_proba = sell_model.predict_proba(df)[:, 1]
                sell_signal = (sell_proba >= sell_threshold).astype(int)
                
                predictions[f"{horizon_name}_sell_proba"] = sell_proba
                predictions[f"{horizon_name}_sell_signal"] = sell_signal
        
        return predictions
    
    def make_decision(self, predictions: Dict) -> Dict:
        """
        ตัดสินใจจากผลลัพธ์การทำนายหลายช่วงเวลา
        
        Args:
            predictions: Dict ของผลลัพธ์การทำนาย
            
        Returns:
            Dict ของการตัดสินใจ
        """
        # ตรวจสอบว่ามีผลลัพธ์การทำนายหรือไม่
        if not predictions:
            logger.warning("No predictions available.")
            return {
                'decision': 'HOLD',
                'confidence': 0.0,
                'reason': 'No predictions available'
            }
        
        # ดึงค่า probabilities และ signals
        short_buy_proba = predictions.get('short_buy_proba', np.array([0.0]))[-1]
        short_sell_proba = predictions.get('short_sell_proba', np.array([0.0]))[-1]
        medium_buy_proba = predictions.get('medium_buy_proba', np.array([0.0]))[-1]
        medium_sell_proba = predictions.get('medium_sell_proba', np.array([0.0]))[-1]
        long_buy_proba = predictions.get('long_buy_proba', np.array([0.0]))[-1]
        long_sell_proba = predictions.get('long_sell_proba', np.array([0.0]))[-1]
        
        short_buy_signal = predictions.get('short_buy_signal', np.array([0]))[-1]
        short_sell_signal = predictions.get('short_sell_signal', np.array([0]))[-1]
        medium_buy_signal = predictions.get('medium_buy_signal', np.array([0]))[-1]
        medium_sell_signal = predictions.get('medium_sell_signal', np.array([0]))[-1]
        long_buy_signal = predictions.get('long_buy_signal', np.array([0]))[-1]
        long_sell_signal = predictions.get('long_sell_signal', np.array([0]))[-1]
        
        # คำนวณคะแนนรวม
        buy_score = (short_buy_proba * 0.2) + (medium_buy_proba * 0.3) + (long_buy_proba * 0.5)
        sell_score = (short_sell_proba * 0.2) + (medium_sell_proba * 0.3) + (long_sell_proba * 0.5)
        
        # นับจำนวนสัญญาณ
        buy_signals = short_buy_signal + medium_buy_signal + long_buy_signal
        sell_signals = short_sell_signal + medium_sell_signal + long_sell_signal
        
        # ตัดสินใจ
        decision = 'HOLD'
        confidence = 0.0
        reason = ''
        
        if buy_score > sell_score and buy_signals >= 2:
            decision = 'BUY'
            confidence = buy_score
            reason = f"Buy score ({buy_score:.4f}) > Sell score ({sell_score:.4f}), {buy_signals}/3 buy signals"
        elif sell_score > buy_score and sell_signals >= 2:
            decision = 'SELL'
            confidence = sell_score
            reason = f"Sell score ({sell_score:.4f}) > Buy score ({buy_score:.4f}), {sell_signals}/3 sell signals"
        else:
            reason = f"No clear signal: Buy score ({buy_score:.4f}), Sell score ({sell_score:.4f}), Buy signals: {buy_signals}/3, Sell signals: {sell_signals}/3"
        
        # สร้างรายละเอียดเพิ่มเติม
        details = {
            'short_term': {
                'buy': {'probability': float(short_buy_proba), 'signal': int(short_buy_signal)},
                'sell': {'probability': float(short_sell_proba), 'signal': int(short_sell_signal)}
            },
            'medium_term': {
                'buy': {'probability': float(medium_buy_proba), 'signal': int(medium_buy_signal)},
                'sell': {'probability': float(medium_sell_proba), 'signal': int(medium_sell_signal)}
            },
            'long_term': {
                'buy': {'probability': float(long_buy_proba), 'signal': int(long_buy_signal)},
                'sell': {'probability': float(long_sell_proba), 'signal': int(long_sell_signal)}
            },
            'scores': {
                'buy_score': float(buy_score),
                'sell_score': float(sell_score)
            }
        }
        
        return {
            'decision': decision,
            'confidence': float(confidence),
            'reason': reason,
            'details': details
        }

# ฟังก์ชันสำหรับใช้งานง่าย
def analyze_multiple_timeframes(df: pd.DataFrame, 
                              symbol: str, 
                              timeframe: str,
                              short_horizon: int = 5, 
                              medium_horizon: int = 10, 
                              long_horizon: int = 15) -> Dict:
    """
    วิเคราะห์ข้อมูลจากหลายช่วงเวลาและตัดสินใจ
    
    Args:
        df: DataFrame ที่มี features
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
        short_horizon: จำนวนแท่งสำหรับช่วงเวลาสั้น
        medium_horizon: จำนวนแท่งสำหรับช่วงเวลากลาง
        long_horizon: จำนวนแท่งสำหรับช่วงเวลายาว
        
    Returns:
        Dict ของการตัดสินใจ
    """
    # สร้าง MultiTimeframeDecisionSystem
    decision_system = MultiTimeframeDecisionSystem(
        short_horizon=short_horizon,
        medium_horizon=medium_horizon,
        long_horizon=long_horizon
    )
    
    # โหลดโมเดล
    decision_system.load_models(symbol, timeframe)
    
    # ทำนายผลลัพธ์
    predictions = decision_system.predict(df, symbol, timeframe)
    
    # ตัดสินใจ
    decision = decision_system.make_decision(predictions)
    
    return decision