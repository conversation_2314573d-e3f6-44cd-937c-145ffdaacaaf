{"analysis_date": "2025-10-06T08:36:33.009258", "total_models_analyzed": 4, "current_thresholds": {"min_accuracy": 0.45, "min_auc": 0.48, "min_f1": 0.08, "min_precision": 0.08, "min_recall": 0.3, "min_win_rate": 0.35, "min_expectancy": 10.0, "min_trades": 8, "improvement_threshold": 0.008, "max_decline_threshold": -0.03}, "recommended_thresholds": {"accuracy": {"threshold": 0.5275564032759856, "mean": 0.5284512077771543, "std": 0.0013187786141904809, "min": 0.5271017162804228, "max": 0.5302408167794918, "count": 3}, "auc": {"threshold": 0.6406793500599142, "mean": 0.6418005920904228, "std": 0.0015859924726082586, "min": 0.6406405285218545, "max": 0.6440430761514403, "count": 3}, "f1": {"threshold": 0.47892369376340244, "mean": 0.48043811851064655, "std": 0.0022294926756974915, "min": 0.47816507037773154, "max": 0.48346696800513483, "count": 3}, "precision": {"threshold": 0.5055387403275516, "mean": 0.5109900105433609, "std": 0.009545080817876228, "min": 0.5003607294591406, "max": 0.5263693732687743, "count": 4}, "recall": {"threshold": 0.5275564032759856, "mean": 0.5284512077771543, "std": 0.0013187786141904809, "min": 0.5271017162804228, "max": 0.5302408167794918, "count": 3}, "win_rate": {"threshold": 0.5225134140437195, "mean": 0.5288881997372412, "std": 0.00797938780687091, "min": 0.5184834171460262, "max": 0.5375984881690528, "count": 4}, "expectancy": {"threshold": 25.28076247392396, "mean": 25.544914611015294, "std": 0.3752206747242623, "min": 25.013146411658795, "max": 25.903534757729098, "count": 4}}, "new_thresholds": {"min_accuracy": 0.5275564032759856, "min_auc": 0.6406793500599142, "min_f1": 0.47892369376340244, "min_precision": 0.5055387403275516, "min_recall": 0.5275564032759856, "min_win_rate": 0.005225134140437195, "min_expectancy": 25.28076247392396, "min_trades": 8, "improvement_threshold": 0.008, "max_decline_threshold": -0.03}, "detailed_results": {"GOLD_M60_trend_following_buy": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_buy", "metrics": [{"accuracy": 0.5280110902715486, "auc": 0.6282230825630994, "f1": 0.46418044530326197, "precision": 0.5072647439503554, "recall": 0.5280110902715486}], "trading_stats": [{"win_rate": 0.5184834171460262, "expectancy": 25.013146411658795, "num_trades": 0}]}, "GOLD_M60_trend_following_sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_sell", "metrics": [{"accuracy": 0.5271017162804228, "auc": 0.640718171597974, "f1": 0.47816507037773154, "precision": 0.5003607294591406, "recall": 0.5271017162804228}], "trading_stats": [{"win_rate": 0.5238567463429507, "expectancy": 25.36996782801235, "num_trades": 0}]}, "GOLD_M60_counter_trend_buy": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_buy", "metrics": [{"accuracy": 0.5302408167794918, "auc": 0.6406405285218545, "f1": 0.47968231714907333, "precision": 0.509965195495173, "recall": 0.5302408167794918}], "trading_stats": [{"win_rate": 0.5356141472909351, "expectancy": 25.893009446660926, "num_trades": 0}]}, "GOLD_M60_counter_trend_sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_sell", "metrics": [{"accuracy": 0.5424482691935796, "auc": 0.6440430761514403, "f1": 0.48346696800513483, "precision": 0.5263693732687743, "recall": 0.5424482691935796}], "trading_stats": [{"win_rate": 0.5375984881690528, "expectancy": 25.903534757729098, "num_trades": 0}]}}}