{"optimization_info": {"symbol": "GENERAL", "timeframe": "ALL", "source": "General optimization results (based on USDJPY_M60)", "score": 56.05, "win_rate": 32.9, "total_profit": 26041, "total_trades": 167, "expectancy": 155.93, "max_drawdown": 22190, "optimization_date": "2025-09-28", "test_period": "Historical backtest data"}, "parameters": {"input_stop_loss_atr": 1.0, "input_take_profit": 2.0, "input_rsi_level_in": 35, "input_volume_spike": 1.25, "input_rsi_level_over": 70, "input_rsi_level_out": 35, "input_pull_back": 0.45, "input_initial_nbar_sl": 4}, "parameter_changes": {}, "performance_metrics": {"before_optimization": {"win_rate": 0.0, "total_profit": 0, "expectancy": 0.0, "max_drawdown": 0}, "after_optimization": {"win_rate": 0.0, "total_profit": 0, "expectancy": 0.0, "max_drawdown": 0}, "improvement": {"win_rate_improvement": 0.0, "profit_improvement": 0, "expectancy_improvement": 0.0, "drawdown_reduction": 0}}, "validation": {"cross_validation_score": 0.0, "out_of_sample_performance": 0.0, "parameter_stability": "High", "recommended_for_live_trading": true}}