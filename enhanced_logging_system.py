"""
Enhanced Logging and Error Handling System
ระบบการบันทึก log และจัดการข้อผิดพลาดแบบครอบคลุม

Features:
- Centralized logging with multiple levels
- UTF-8 support for Thai text
- File rotation and size management
- Error tracking and reporting
- Performance monitoring
- Integration with existing systems
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import io


class EnhancedLogger:
    """ระบบ logging แบบครอบคลุมสำหรับการเทรนและวิเคราะห์"""
    
    def __init__(self, name: str = "TradingSystem", log_dir: str = "Logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # สร้าง logger หลัก
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # ป้องกันการสร้าง handler ซ้ำ
        if not self.logger.handlers:
            self._setup_handlers()
        
        # เก็บสถิติการใช้งาน
        self.stats = {
            'errors': 0,
            'warnings': 0,
            'info': 0,
            'debug': 0,
            'start_time': datetime.now()
        }
        
        # เก็บข้อผิดพลาดล่าสุด
        self.recent_errors = []
        self.max_recent_errors = 50
        
        self.info("🚀 Enhanced Logging System เริ่มทำงาน")
    
    def _setup_handlers(self):
        """ตั้งค่า handlers สำหรับ logging"""
        
        # Formatter สำหรับ Thai text
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Console Handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # File Handler - Main Log
        main_log_file = self.log_dir / f"{self.name}_main.log"
        file_handler = logging.FileHandler(main_log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # File Handler - Error Log
        error_log_file = self.log_dir / f"{self.name}_errors.log"
        error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        
        # เพิ่ม handlers
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
    
    def debug(self, message: str, extra_data: Dict = None):
        """บันทึก debug message"""
        self.stats['debug'] += 1
        self.logger.debug(self._format_message(message, extra_data))
    
    def info(self, message: str, extra_data: Dict = None):
        """บันทึก info message"""
        self.stats['info'] += 1
        self.logger.info(self._format_message(message, extra_data))
    
    def warning(self, message: str, extra_data: Dict = None):
        """บันทึก warning message"""
        self.stats['warnings'] += 1
        self.logger.warning(self._format_message(message, extra_data))
    
    def error(self, message: str, exception: Exception = None, extra_data: Dict = None):
        """บันทึก error message พร้อม exception details"""
        self.stats['errors'] += 1
        
        # สร้าง error record
        error_record = {
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'extra_data': extra_data or {}
        }
        
        if exception:
            error_record['exception'] = {
                'type': type(exception).__name__,
                'message': str(exception),
                'traceback': traceback.format_exc()
            }
        
        # เพิ่มใน recent errors
        self.recent_errors.append(error_record)
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors.pop(0)
        
        # Log ข้อความ
        full_message = self._format_message(message, extra_data)
        if exception:
            full_message += f"\n🔍 Exception: {type(exception).__name__}: {str(exception)}"
            full_message += f"\n📋 Traceback:\n{traceback.format_exc()}"
        
        self.logger.error(full_message)
    
    def _format_message(self, message: str, extra_data: Dict = None) -> str:
        """จัดรูปแบบข้อความ"""
        if extra_data:
            extra_str = " | ".join([f"{k}={v}" for k, v in extra_data.items()])
            return f"{message} | {extra_str}"
        return message
    
    def log_function_start(self, function_name: str, **kwargs):
        """บันทึกการเริ่มต้นฟังก์ชัน"""
        self.info(f"🏗️ เปิดใช้งาน {function_name}", kwargs)
    
    def log_function_end(self, function_name: str, duration: float = None, **kwargs):
        """บันทึกการสิ้นสุดฟังก์ชัน"""
        extra_data = kwargs.copy()
        if duration:
            extra_data['duration_seconds'] = f"{duration:.2f}"
        self.info(f"✅ เสร็จสิ้น {function_name}", extra_data)
    
    def log_performance(self, operation: str, duration: float, **metrics):
        """บันทึกประสิทธิภาพการทำงาน"""
        perf_data = {
            'operation': operation,
            'duration_seconds': f"{duration:.2f}",
            **metrics
        }
        self.info(f"⏱️ Performance: {operation}", perf_data)
    
    def log_model_training(self, symbol: str, timeframe: str, **metrics):
        """บันทึกผลการเทรนโมเดล"""
        training_data = {
            'symbol': symbol,
            'timeframe': timeframe,
            **metrics
        }
        self.info(f"🤖 Model Training: {symbol}_{timeframe}", training_data)
    
    def log_financial_analysis(self, symbol: str, timeframe: str, **metrics):
        """บันทึกผลการวิเคราะห์ทางการเงิน"""
        financial_data = {
            'symbol': symbol,
            'timeframe': timeframe,
            **metrics
        }
        self.info(f"💰 Financial Analysis: {symbol}_{timeframe}", financial_data)
    
    def create_error_report(self) -> str:
        """สร้างรายงานข้อผิดพลาด"""
        report_path = self.log_dir / f"error_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("ERROR REPORT - ระบบการเทรนและวิเคราะห์\n")
            f.write("=" * 80 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"System: {self.name}\n\n")
            
            # สถิติรวม
            f.write("📊 STATISTICS\n")
            f.write("-" * 40 + "\n")
            runtime = datetime.now() - self.stats['start_time']
            f.write(f"Runtime: {runtime}\n")
            f.write(f"Errors: {self.stats['errors']}\n")
            f.write(f"Warnings: {self.stats['warnings']}\n")
            f.write(f"Info Messages: {self.stats['info']}\n")
            f.write(f"Debug Messages: {self.stats['debug']}\n\n")
            
            # ข้อผิดพลาดล่าสุด
            f.write("🚨 RECENT ERRORS\n")
            f.write("-" * 40 + "\n")
            if not self.recent_errors:
                f.write("No recent errors found.\n")
            else:
                for i, error in enumerate(self.recent_errors[-10:], 1):  # แสดง 10 รายการล่าสุด
                    f.write(f"\n{i}. {error['timestamp']}\n")
                    f.write(f"   Message: {error['message']}\n")
                    if 'exception' in error:
                        f.write(f"   Exception: {error['exception']['type']}: {error['exception']['message']}\n")
                    if error['extra_data']:
                        f.write(f"   Extra Data: {error['extra_data']}\n")
        
        self.info(f"📄 สร้างรายงานข้อผิดพลาด: {report_path}")
        return str(report_path)
    
    def cleanup_old_logs(self, days_to_keep: int = 7):
        """ลบ log files เก่า"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        cleaned_count = 0
        for log_file in self.log_dir.glob("*.log"):
            if log_file.stat().st_mtime < cutoff_date.timestamp():
                log_file.unlink()
                cleaned_count += 1
        
        if cleaned_count > 0:
            self.info(f"🧹 ลบ log files เก่า: {cleaned_count} ไฟล์")
    
    def get_stats(self) -> Dict:
        """ดึงสถิติการใช้งาน"""
        runtime = datetime.now() - self.stats['start_time']
        return {
            **self.stats,
            'runtime_seconds': runtime.total_seconds(),
            'recent_errors_count': len(self.recent_errors)
        }


# สร้าง logger หลักสำหรับระบบ
main_logger = EnhancedLogger("TradingSystem")


# Decorator สำหรับ logging ฟังก์ชัน
def log_function(logger: EnhancedLogger = None):
    """Decorator สำหรับ log การทำงานของฟังก์ชัน"""
    if logger is None:
        logger = main_logger
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            start_time = datetime.now()
            
            try:
                logger.log_function_start(func_name, **kwargs)
                result = func(*args, **kwargs)
                
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_function_end(func_name, duration)
                
                return result
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(f"ข้อผิดพลาดใน {func_name}", e, {
                    'duration_seconds': duration,
                    'args_count': len(args),
                    'kwargs': list(kwargs.keys())
                })
                raise
        
        return wrapper
    return decorator


# Context manager สำหรับ logging operations
class LoggedOperation:
    """Context manager สำหรับ log การทำงาน"""
    
    def __init__(self, operation_name: str, logger: EnhancedLogger = None, **context):
        self.operation_name = operation_name
        self.logger = logger or main_logger
        self.context = context
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.info(f"🚀 เริ่ม {self.operation_name}", self.context)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.log_performance(self.operation_name, duration, **self.context)
        else:
            self.logger.error(f"ข้อผิดพลาดใน {self.operation_name}", exc_val, {
                'duration_seconds': duration,
                **self.context
            })
        
        return False  # Don't suppress exceptions


if __name__ == "__main__":
    # ทดสอบระบบ logging
    logger = EnhancedLogger("TestSystem")
    
    logger.info("ทดสอบระบบ logging")
    logger.warning("ทดสอบ warning")
    
    try:
        raise ValueError("ทดสอบ error")
    except Exception as e:
        logger.error("ทดสอบการบันทึก error", e, {'test_data': 'example'})
    
    # ทดสอบ decorator
    @log_function(logger)
    def test_function(x, y=10):
        return x + y
    
    result = test_function(5, y=15)
    
    # ทดสอบ context manager
    with LoggedOperation("Test Operation", logger, param1="value1"):
        import time
        time.sleep(0.1)
    
    # สร้างรายงาน
    report_path = logger.create_error_report()
    print(f"รายงานถูกสร้างที่: {report_path}")
    
    # แสดงสถิติ
    stats = logger.get_stats()
    print(f"สถิติ: {stats}")
