{"optimization_info": {"symbol": "USDJPY", "timeframe": "M60", "source": "USDJPY_M60 specific optimization", "score": 56.05, "win_rate": 32.9, "total_profit": 26041, "total_trades": 167, "expectancy": 155.93, "max_drawdown": 22190, "optimization_date": "2024-12-28", "test_period": "Historical backtest data"}, "parameters": {"input_stop_loss_atr": 1.0, "input_take_profit": 2.0, "input_rsi_level_in": 35, "input_volume_spike": 1.25, "input_rsi_level_over": 70, "input_rsi_level_out": 35, "input_pull_back": 0.45, "input_initial_nbar_sl": 4}, "parameter_changes": {"input_stop_loss_atr": {"old_value": 1.25, "new_value": 1.0, "change": "decreased", "impact": "Tighter stop loss to reduce risk"}, "input_take_profit": {"old_value": 3.0, "new_value": 2.0, "change": "decreased", "impact": "Lower TP ratio for better hit rate"}, "input_rsi_level_in": {"old_value": 40, "new_value": 35, "change": "decreased", "impact": "More sensitive entry signals"}, "input_volume_spike": {"old_value": 1.5, "new_value": 1.25, "change": "decreased", "impact": "Lower volume requirement for entry"}, "input_rsi_level_out": {"old_value": 30, "new_value": 35, "change": "increased", "impact": "Earlier exit from positions"}}, "performance_metrics": {"before_optimization": {"win_rate": 28.5, "total_profit": 18500, "expectancy": 125.3, "max_drawdown": 28500}, "after_optimization": {"win_rate": 32.9, "total_profit": 26041, "expectancy": 155.93, "max_drawdown": 22190}, "improvement": {"win_rate_improvement": 4.4, "profit_improvement": 7541, "expectancy_improvement": 30.63, "drawdown_reduction": 6310}}, "validation": {"cross_validation_score": 0.78, "out_of_sample_performance": 0.82, "parameter_stability": "High", "recommended_for_live_trading": true}}