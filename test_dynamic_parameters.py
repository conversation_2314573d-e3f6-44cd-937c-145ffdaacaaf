#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Test Dynamic Parameter Loading
=================================

สคริปต์สำหรับทดสอบการโหลดพารามิเตอร์แบบไดนามิกตาม symbol และ timeframe
"""

import sys
import os

# เพิ่ม path สำหรับ import
sys.path.append('.')

# Import ฟังก์ชันจาก WebRequest_Server_06.py
try:
    from WebRequest_Server_06 import (
        load_dynamic_parameters_for_symbol,
        convert_timeframe_to_string,
        clear_parameter_cache,
        load_optimization_parameters
    )
    print("✅ Successfully imported functions from WebRequest_Server_06.py")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    print("💡 Make sure WebRequest_Server_06.py is in the current directory")
    sys.exit(1)

def test_timeframe_conversion():
    """ทดสอบการแปลง timeframe"""
    print(f"\n🔄 Testing Timeframe Conversion")
    print(f"=" * 40)
    
    test_cases = [30, 60, 240, 1440, 15, 5]
    
    for tf_int in test_cases:
        tf_str = convert_timeframe_to_string(tf_int)
        print(f"   {tf_int} → {tf_str}")

def test_optimization_file_loading():
    """ทดสอบการโหลดไฟล์ optimization results"""
    print(f"\n📁 Testing Optimization File Loading")
    print(f"=" * 40)
    
    test_cases = [
        ("USDJPY", "M60"),
        ("GOLD", "M30"),
        ("GOLD", "M60"),
        ("EURUSD", "M30"),
        ("EURUSD", "M60"),
        ("GBPUSD", "M30"),  # ไฟล์ไม่มี
        ("UNKNOWN", "M15")  # ไฟล์ไม่มี
    ]
    
    for symbol, timeframe in test_cases:
        print(f"\n🔍 Testing {symbol} {timeframe}:")
        params = load_optimization_parameters(symbol=symbol, timeframe=timeframe)
        if params:
            print(f"   ✅ Found optimization file")
            print(f"   📊 Parameters:")
            for key, value in params.items():
                if key.startswith('input_'):
                    print(f"      {key}: {value}")
        else:
            print(f"   ⚠️ No optimization file found")

def test_dynamic_parameter_loading():
    """ทดสอบการโหลดพารามิเตอร์แบบไดนามิก"""
    print(f"\n🎯 Testing Dynamic Parameter Loading")
    print(f"=" * 40)
    
    test_cases = [
        ("USDJPY", 60),
        ("GOLD", 30),
        ("GOLD", 60),
        ("EURUSD", 30),
        ("EURUSD", 60),
        ("GBPUSD", 30),  # ไฟล์ไม่มี - ควรใช้ default
        ("UNKNOWN", 15)  # ไฟล์ไม่มี - ควรใช้ default
    ]
    
    for symbol, timeframe_int in test_cases:
        print(f"\n🔍 Testing {symbol} M{timeframe_int}:")
        params = load_dynamic_parameters_for_symbol(symbol, timeframe_int)
        
        print(f"   📊 Loaded parameters:")
        for key, value in params.items():
            print(f"      {key}: {value}")

def test_parameter_cache():
    """ทดสอบ parameter cache"""
    print(f"\n💾 Testing Parameter Cache")
    print(f"=" * 40)
    
    symbol = "USDJPY"
    timeframe = 60
    
    print(f"🔄 First load (should read from file):")
    params1 = load_dynamic_parameters_for_symbol(symbol, timeframe)
    
    print(f"\n🔄 Second load (should use cache):")
    params2 = load_dynamic_parameters_for_symbol(symbol, timeframe)
    
    print(f"\n🧹 Clearing cache:")
    clear_parameter_cache()
    
    print(f"\n🔄 Third load (should read from file again):")
    params3 = load_dynamic_parameters_for_symbol(symbol, timeframe)
    
    # ตรวจสอบว่าค่าเหมือนกัน
    if params1 == params2 == params3:
        print(f"✅ Cache working correctly - all parameters match")
    else:
        print(f"❌ Cache issue - parameters don't match")

def test_multiple_symbols_simultaneously():
    """ทดสอบการโหลดหลาย symbol พร้อมกัน"""
    print(f"\n🔀 Testing Multiple Symbols Simultaneously")
    print(f"=" * 40)
    
    symbols_timeframes = [
        ("USDJPY", 60),
        ("GOLD", 30),
        ("EURUSD", 60),
        ("GOLD", 60),
        ("EURUSD", 30)
    ]
    
    all_params = {}
    
    for symbol, timeframe in symbols_timeframes:
        key = f"{symbol}_M{timeframe}"
        params = load_dynamic_parameters_for_symbol(symbol, timeframe)
        all_params[key] = params
        print(f"✅ Loaded {key}")
    
    print(f"\n📊 Summary of all loaded parameters:")
    for key, params in all_params.items():
        print(f"\n{key}:")
        print(f"   SL ATR: {params['input_stop_loss_atr']}")
        print(f"   TP Ratio: {params['input_take_profit']}")
        print(f"   RSI In: {params['input_rsi_level_in']}")
        print(f"   Volume Spike: {params['input_volume_spike']}")

def compare_parameters():
    """เปรียบเทียบพารามิเตอร์ระหว่าง symbol/timeframe ต่างๆ"""
    print(f"\n📊 Parameter Comparison")
    print(f"=" * 40)
    
    symbols_timeframes = [
        ("USDJPY", 60),
        ("GOLD", 30),
        ("GOLD", 60),
        ("EURUSD", 30),
        ("EURUSD", 60)
    ]
    
    print(f"{'Symbol_TF':<12} {'SL_ATR':<8} {'TP_Ratio':<9} {'RSI_In':<7} {'Vol_Spike':<10}")
    print(f"-" * 50)
    
    for symbol, timeframe in symbols_timeframes:
        params = load_dynamic_parameters_for_symbol(symbol, timeframe)
        key = f"{symbol}_M{timeframe}"
        
        print(f"{key:<12} {params['input_stop_loss_atr']:<8} "
              f"{params['input_take_profit']:<9} {params['input_rsi_level_in']:<7} "
              f"{params['input_volume_spike']:<10}")

def run_all_tests():
    """รันการทดสอบทั้งหมด"""
    print(f"🧪 Dynamic Parameter Loading Test Suite")
    print(f"=" * 60)
    
    tests = [
        ("Timeframe Conversion", test_timeframe_conversion),
        ("Optimization File Loading", test_optimization_file_loading),
        ("Dynamic Parameter Loading", test_dynamic_parameter_loading),
        ("Parameter Cache", test_parameter_cache),
        ("Multiple Symbols", test_multiple_symbols_simultaneously),
        ("Parameter Comparison", compare_parameters)
    ]
    
    for test_name, test_func in tests:
        print(f"\n" + "="*60)
        print(f"🧪 Running Test: {test_name}")
        try:
            test_func()
            print(f"✅ {test_name}: PASSED")
        except Exception as e:
            print(f"❌ {test_name}: FAILED - {e}")
            import traceback
            traceback.print_exc()

def interactive_test():
    """โหมดทดสอบแบบ Interactive"""
    while True:
        print(f"\n🧪 Dynamic Parameter Test Menu")
        print(f"=" * 40)
        print(f"1. 🔄 Test Timeframe Conversion")
        print(f"2. 📁 Test File Loading")
        print(f"3. 🎯 Test Dynamic Loading")
        print(f"4. 💾 Test Cache")
        print(f"5. 🔀 Test Multiple Symbols")
        print(f"6. 📊 Compare Parameters")
        print(f"7. 🚀 Run All Tests")
        print(f"8. 🚪 Exit")
        
        choice = input(f"\nSelect option (1-8): ").strip()
        
        if choice == '1':
            test_timeframe_conversion()
        elif choice == '2':
            test_optimization_file_loading()
        elif choice == '3':
            test_dynamic_parameter_loading()
        elif choice == '4':
            test_parameter_cache()
        elif choice == '5':
            test_multiple_symbols_simultaneously()
        elif choice == '6':
            compare_parameters()
        elif choice == '7':
            run_all_tests()
        elif choice == '8':
            print(f"👋 Goodbye!")
            break
        else:
            print(f"❌ Invalid option. Please select 1-8.")

if __name__ == "__main__":
    print(f"🧪 Dynamic Parameter Loading Tester")
    print(f"=" * 50)
    
    mode = input(f"Select mode:\n1. 🚀 Run all tests\n2. 🎮 Interactive mode\nChoice (1-2): ").strip()
    
    if mode == '1':
        run_all_tests()
    elif mode == '2':
        interactive_test()
    else:
        print(f"❌ Invalid choice. Running all tests by default.")
        run_all_tests()
