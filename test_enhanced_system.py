import pandas as pd
import numpy as np
import os
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import Dict, List

# นำเข้าโมดูลที่เราสร้างขึ้น
from enhanced_feature_creation import EnhancedFeatureCreator, improved_process_trade_targets
from multi_timeframe_decision_system import MultiTimeframeDecisionSystem, analyze_multiple_timeframes

# ตั้งค่า logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("Logs/test_enhanced_system.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("TestEnhancedSystem")

def load_data(symbol: str, timeframe: str, data_dir: str = "Data") -> pd.DataFrame:
    """
    โหลดข้อมูลจากไฟล์
    
    Args:
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
        data_dir: ไดเรกทอรีข้อมูล
        
    Returns:
        DataFrame ของข้อมูล
    """
    file_path = os.path.join(data_dir, f"{symbol}_{timeframe}.csv")
    
    if not os.path.exists(file_path):
        logger.error(f"Data file not found: {file_path}")
        return None
    
    df = pd.read_csv(file_path)
    
    # แปลงคอลัมน์ DateTime เป็น datetime
    if 'DateTime' in df.columns:
        df['DateTime'] = pd.to_datetime(df['DateTime'])
    
    logger.info(f"Loaded data for {symbol} {timeframe}: {len(df)} rows")
    return df

def test_enhanced_feature_creation(df: pd.DataFrame, symbol: str, timeframe: str) -> Dict[str, pd.DataFrame]:
    """
    ทดสอบการสร้าง features แบบใหม่
    
    Args:
        df: DataFrame ของข้อมูล
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
        
    Returns:
        Dict ของ DataFrames แยกตามสถานการณ์
    """
    logger.info("Testing enhanced feature creation...")
    
    # สร้าง EnhancedFeatureCreator
    creator = EnhancedFeatureCreator(
        short_horizon=5,
        medium_horizon=10,
        long_horizon=15
    )
    
    # สร้าง features
    df_with_features = creator.create_base_features(df)
    
    # สร้าง Next_Close และ targets
    df_with_targets = creator.create_next_close_features(df_with_features)
    
    # สร้างข้อมูลสำหรับแต่ละสถานการณ์
    scenario_dfs = creator.create_market_scenarios(df_with_targets)
    
    # แสดงข้อมูลสถิติ
    for scenario, scenario_df in scenario_dfs.items():
        logger.info(f"Scenario: {scenario}, Shape: {scenario_df.shape}")
        
        # แสดงจำนวนข้อมูลสำหรับแต่ละ target
        for horizon in [5, 10, 15]:
            for target_type in ['Buy', 'Sell']:
                target_col = f'Target_{target_type}_{horizon}'
                if target_col in scenario_df.columns:
                    positive_count = scenario_df[target_col].sum()
                    total_count = len(scenario_df)
                    percentage = (positive_count / total_count) * 100
                    logger.info(f"  {target_col}: {positive_count}/{total_count} ({percentage:.2f}%)")
    
    return scenario_dfs

def test_improved_trade_targets(df: pd.DataFrame, symbol: str, timeframe: str) -> pd.DataFrame:
    """
    ทดสอบการสร้าง trade targets แบบปรับปรุง
    
    Args:
        df: DataFrame ของข้อมูล
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
        
    Returns:
        DataFrame ที่มี trade targets
    """
    logger.info("Testing improved trade targets...")
    
    # สร้าง trade targets
    trade_df = improved_process_trade_targets(df, symbol, timeframe)
    
    # แสดงข้อมูลสถิติ
    if 'Main_Target' in trade_df.columns:
        target_counts = trade_df['Main_Target'].value_counts()
        logger.info(f"Main_Target distribution: {target_counts.to_dict()}")
    
    if 'Target_Buy' in trade_df.columns:
        buy_count = trade_df['Target_Buy'].sum()
        total_count = len(trade_df)
        buy_percentage = (buy_count / total_count) * 100
        logger.info(f"Target_Buy: {buy_count}/{total_count} ({buy_percentage:.2f}%)")
    
    if 'Target_Sell' in trade_df.columns:
        sell_count = trade_df['Target_Sell'].sum()
        total_count = len(trade_df)
        sell_percentage = (sell_count / total_count) * 100
        logger.info(f"Target_Sell: {sell_count}/{total_count} ({sell_percentage:.2f}%)")
    
    return trade_df

def test_model_training(scenario_dfs: Dict[str, pd.DataFrame], symbol: str, timeframe: str) -> Dict:
    """
    ทดสอบการเทรนโมเดล
    
    Args:
        scenario_dfs: Dict ของ DataFrames แยกตามสถานการณ์
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
        
    Returns:
        Dict ของผลลัพธ์การเทรน
    """
    logger.info("Testing model training...")
    
    # สร้าง MultiTimeframeDecisionSystem
    decision_system = MultiTimeframeDecisionSystem(
        short_horizon=5,
        medium_horizon=10,
        long_horizon=15
    )
    
    # เทรนโมเดล
    results = decision_system.train_models(
        scenario_dfs,
        symbol,
        timeframe
    )
    
    # แสดงผลลัพธ์
    for model_key, model_results in results.items():
        logger.info(f"Model: {model_key}")
        logger.info(f"  Accuracy: {model_results['accuracy']:.4f}")
        logger.info(f"  Precision: {model_results['precision']:.4f}")
        logger.info(f"  Recall: {model_results['recall']:.4f}")
        logger.info(f"  F1: {model_results['f1']:.4f}")
        logger.info(f"  Best Threshold: {model_results['best_threshold']:.4f}")
        
        # แสดง feature importance
        top_features = sorted(
            model_results['feature_importance'].items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        logger.info("  Top 10 Features:")
        for feature, importance in top_features:
            logger.info(f"    {feature}: {importance:.4f}")
    
    return results

def test_prediction_and_decision(df: pd.DataFrame, symbol: str, timeframe: str) -> Dict:
    """
    ทดสอบการทำนายและตัดสินใจ
    
    Args:
        df: DataFrame ของข้อมูล
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
        
    Returns:
        Dict ของการตัดสินใจ
    """
    logger.info("Testing prediction and decision...")
    
    # สร้าง EnhancedFeatureCreator
    creator = EnhancedFeatureCreator(
        short_horizon=5,
        medium_horizon=10,
        long_horizon=15
    )
    
    # สร้าง features
    df_with_features = creator.create_base_features(df)
    
    # ลบคอลัมน์ที่ไม่ใช่ features
    drop_cols = ['DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']
    features_df = df_with_features.drop(columns=drop_cols, errors='ignore')
    
    # ทำนายและตัดสินใจ
    decision = analyze_multiple_timeframes(
        features_df,
        symbol,
        timeframe
    )
    
    # แสดงผลลัพธ์
    logger.info(f"Decision: {decision['decision']}")
    logger.info(f"Confidence: {decision['confidence']:.4f}")
    logger.info(f"Reason: {decision['reason']}")
    
    return decision

def visualize_results(df: pd.DataFrame, scenario_dfs: Dict[str, pd.DataFrame], results: Dict, symbol: str, timeframe: str):
    """
    แสดงผลลัพธ์ด้วยกราฟ
    
    Args:
        df: DataFrame ของข้อมูล
        scenario_dfs: Dict ของ DataFrames แยกตามสถานการณ์
        results: Dict ของผลลัพธ์การเทรน
        symbol: สัญลักษณ์
        timeframe: ไทม์เฟรม
    """
    # สร้างไดเรกทอรีสำหรับบันทึกกราฟ
    output_dir = "Results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. แสดงราคาและ EMA200
    plt.figure(figsize=(12, 6))
    plt.plot(df['DateTime'], df['Close'], label='Close')
    if 'EMA200' in df.columns:
        plt.plot(df['DateTime'], df['EMA200'], label='EMA200')
    plt.title(f"{symbol} {timeframe} - Price and EMA200")
    plt.xlabel("DateTime")
    plt.ylabel("Price")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, f"{symbol}_{timeframe}_price_ema200.png"))
    
    # 2. แสดงจำนวนข้อมูลในแต่ละสถานการณ์
    scenario_counts = {scenario: len(df) for scenario, df in scenario_dfs.items()}
    plt.figure(figsize=(10, 6))
    plt.bar(scenario_counts.keys(), scenario_counts.values())
    plt.title(f"{symbol} {timeframe} - Data Count by Scenario")
    plt.xlabel("Scenario")
    plt.ylabel("Count")
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"{symbol}_{timeframe}_scenario_counts.png"))
    
    # 3. แสดง F1 scores ของแต่ละโมเดล
    model_names = []
    f1_scores = []
    
    for model_key, model_results in results.items():
        model_names.append(model_key)
        f1_scores.append(model_results['f1'])
    
    plt.figure(figsize=(12, 6))
    plt.bar(model_names, f1_scores)
    plt.title(f"{symbol} {timeframe} - F1 Scores by Model")
    plt.xlabel("Model")
    plt.ylabel("F1 Score")
    plt.xticks(rotation=90)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"{symbol}_{timeframe}_f1_scores.png"))
    
    # 4. แสดง feature importance ของโมเดลแรก
    if results:
        first_model_key = list(results.keys())[0]
        feature_importance = results[first_model_key]['feature_importance']
        
        top_features = sorted(
            feature_importance.items(),
            key=lambda x: x[1],
            reverse=True
        )[:20]
        
        feature_names = [feature for feature, _ in top_features]
        importance_values = [importance for _, importance in top_features]
        
        plt.figure(figsize=(12, 8))
        plt.barh(feature_names, importance_values)
        plt.title(f"{symbol} {timeframe} - {first_model_key} - Top 20 Feature Importance")
        plt.xlabel("Importance")
        plt.ylabel("Feature")
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f"{symbol}_{timeframe}_feature_importance.png"))

def main():
    """ฟังก์ชันหลัก"""
    # กำหนดพารามิเตอร์
    symbol = "XAUUSD"
    timeframe = "H1"
    
    # สร้างไดเรกทอรีสำหรับ logs
    os.makedirs("Logs", exist_ok=True)
    
    # โหลดข้อมูล
    df = load_data(symbol, timeframe)
    
    if df is None:
        logger.error("Failed to load data. Exiting...")
        return
    
    # ทดสอบการสร้าง features แบบใหม่
    scenario_dfs = test_enhanced_feature_creation(df, symbol, timeframe)
    
    # ทดสอบการสร้าง trade targets แบบปรับปรุง
    trade_df = test_improved_trade_targets(df, symbol, timeframe)
    
    # ทดสอบการเทรนโมเดล
    results = test_model_training(scenario_dfs, symbol, timeframe)
    
    # ทดสอบการทำนายและตัดสินใจ
    decision = test_prediction_and_decision(df, symbol, timeframe)
    
    # แสดงผลลัพธ์ด้วยกราฟ
    visualize_results(df, scenario_dfs, results, symbol, timeframe)
    
    logger.info("Testing completed successfully!")

if __name__ == "__main__":
    main()