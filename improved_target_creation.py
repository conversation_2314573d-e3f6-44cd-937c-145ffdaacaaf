#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ปรับปรุงการสร้าง Target_Buy และ Target_Sell ให้มีการกระจายที่ดีขึ้น
สำหรับแก้ไขปัญหา Multi-Model Architecture ที่ได้เพียง 1 โมเดลจาก 6 โมเดล
"""

import pandas as pd
import numpy as np

def create_improved_targets(df, horizon=5, method='flexible'):
    """
    สร้าง Target_Buy และ Target_Sell ที่ปรับปรุงแล้ว
    
    Args:
        df: DataFrame ที่มีข้อมูล OHLC
        horizon: จำนวน bars ที่มองไปข้างหน้า
        method: วิธีการสร้าง target ('flexible', 'balanced', 'conservative')
    
    Returns:
        DataFrame ที่มี Target_Buy และ Target_Sell ที่ปรับปรุงแล้ว
    """
    
    df = df.copy()
    
    # สร้างข้อมูลราคาในอนาคต
    df['Next_Close'] = df['Close'].shift(-horizon)
    df['Next_High'] = df['High'].shift(-horizon)
    df['Next_Low'] = df['Low'].shift(-horizon)
    
    # คำนวณ price movement
    df['Price_Change'] = (df['Next_Close'] - df['Close']) / df['Close']
    df['Price_Change_Pct'] = df['Price_Change'] * 100
    
    if method == 'flexible':
        # วิธีที่ยืดหยุ่น - ใช้ percentile แทนเงื่อนไขแข็ง
        buy_threshold = df['Price_Change_Pct'].quantile(0.6)  # 60th percentile
        sell_threshold = df['Price_Change_Pct'].quantile(0.4)  # 40th percentile
        
        # Target_Buy: 1 ถ้าราคาขึ้นมากกว่า threshold
        df['Target_Buy'] = (df['Price_Change_Pct'] > buy_threshold).astype(int)
        
        # Target_Sell: 1 ถ้าราคาลงมากกว่า threshold (ใช้ค่าลบ)
        df['Target_Sell'] = (df['Price_Change_Pct'] < sell_threshold).astype(int)
        
    elif method == 'balanced':
        # วิธีที่สมดุล - ใช้ standard deviation
        price_std = df['Price_Change_Pct'].std()
        price_mean = df['Price_Change_Pct'].mean()
        
        buy_threshold = price_mean + (price_std * 0.5)
        sell_threshold = price_mean - (price_std * 0.5)
        
        df['Target_Buy'] = (df['Price_Change_Pct'] > buy_threshold).astype(int)
        df['Target_Sell'] = (df['Price_Change_Pct'] < sell_threshold).astype(int)
        
    elif method == 'conservative':
        # วิธีที่อนุรักษ์นิยม - ใช้เงื่อนไขที่ชัดเจน
        # แต่ปรับให้ยืดหยุ่นกว่าเดิม
        
        # เงื่อนไข Buy: ราคาปิดสูงกว่าราคาเปิด และราคาในอนาคตสูงขึ้น
        buy_condition = (
            (df['Close'] > df['Open']) &  # แท่งเขียว
            (df['Next_Close'] > df['Close'] * 1.001)  # ราคาขึ้นอย่างน้อย 0.1%
        )
        
        # เงื่อนไข Sell: ราคาปิดต่ำกว่าราคาเปิด และราคาในอนาคตลดลง
        sell_condition = (
            (df['Close'] < df['Open']) &  # แท่งแดง
            (df['Next_Close'] < df['Close'] * 0.999)  # ราคาลงอย่างน้อย 0.1%
        )
        
        df['Target_Buy'] = buy_condition.astype(int)
        df['Target_Sell'] = sell_condition.astype(int)
    
    # ลบแถวที่มี NaN
    df.dropna(subset=['Next_Close', 'Target_Buy', 'Target_Sell'], inplace=True)
    
    # แสดงสถิติ
    print(f"\n📊 Target Distribution (Method: {method}):")
    print(f"   Target_Buy: {df['Target_Buy'].value_counts().to_dict()}")
    print(f"   Target_Sell: {df['Target_Sell'].value_counts().to_dict()}")
    
    buy_ratio = df['Target_Buy'].mean()
    sell_ratio = df['Target_Sell'].mean()
    print(f"   Buy Ratio: {buy_ratio:.3f} ({buy_ratio*100:.1f}%)")
    print(f"   Sell Ratio: {sell_ratio:.3f} ({sell_ratio*100:.1f}%)")
    
    return df

def analyze_target_quality(df):
    """
    วิเคราะห์คุณภาพของ Target ที่สร้างขึ้น
    """
    print(f"\n🔍 Target Quality Analysis:")
    print(f"   Total samples: {len(df):,}")
    
    # ตรวจสอบ Target_Buy
    buy_counts = df['Target_Buy'].value_counts()
    if len(buy_counts) >= 2:
        buy_balance = min(buy_counts) / max(buy_counts)
        print(f"   Target_Buy balance: {buy_balance:.3f} (closer to 1.0 is better)")
    else:
        print(f"   Target_Buy: ⚠️ Only one class found: {buy_counts.to_dict()}")
    
    # ตรวจสอบ Target_Sell
    sell_counts = df['Target_Sell'].value_counts()
    if len(sell_counts) >= 2:
        sell_balance = min(sell_counts) / max(sell_counts)
        print(f"   Target_Sell balance: {sell_balance:.3f} (closer to 1.0 is better)")
    else:
        print(f"   Target_Sell: ⚠️ Only one class found: {sell_counts.to_dict()}")
    
    return {
        'buy_classes': len(buy_counts),
        'sell_classes': len(sell_counts),
        'buy_balance': buy_balance if len(buy_counts) >= 2 else 0,
        'sell_balance': sell_balance if len(sell_counts) >= 2 else 0
    }

def test_target_methods(csv_file):
    """
    ทดสอบวิธีการสร้าง Target ต่างๆ
    """
    print(f"🧪 Testing Target Creation Methods for: {csv_file}")
    print("="*80)
    
    # อ่านข้อมูล
    df = pd.read_csv(csv_file, low_memory=False)
    print(f"📊 Original data: {len(df):,} rows")

    # แปลงคอลัมน์ราคาเป็นตัวเลข
    price_columns = ['Open', 'High', 'Low', 'Close']
    for col in price_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

    # ลบแถวที่มี NaN ในคอลัมน์ราคา
    df.dropna(subset=price_columns, inplace=True)
    print(f"📊 After cleaning: {len(df):,} rows")
    
    methods = ['flexible', 'balanced', 'conservative']
    results = {}
    
    for method in methods:
        print(f"\n{'='*50}")
        print(f"Testing Method: {method.upper()}")
        print(f"{'='*50}")
        
        df_test = create_improved_targets(df, horizon=5, method=method)
        quality = analyze_target_quality(df_test)
        results[method] = quality
    
    # สรุปผล
    print(f"\n{'='*80}")
    print(f"📋 SUMMARY - Best Methods for Multi-Model Training:")
    print(f"{'='*80}")
    
    for method, quality in results.items():
        score = (quality['buy_classes'] >= 2) + (quality['sell_classes'] >= 2)
        score += quality['buy_balance'] + quality['sell_balance']
        
        print(f"{method.upper():>12}: Score={score:.2f}, "
              f"Buy_Classes={quality['buy_classes']}, "
              f"Sell_Classes={quality['sell_classes']}")
    
    # แนะนำ
    best_method = max(results.keys(), key=lambda k: 
                     (results[k]['buy_classes'] >= 2) + (results[k]['sell_classes'] >= 2) +
                     results[k]['buy_balance'] + results[k]['sell_balance'])
    
    print(f"\n🎯 RECOMMENDATION: Use '{best_method.upper()}' method")
    print(f"   This method provides the best balance for Multi-Model training")
    
    return results, best_method

if __name__ == "__main__":
    # ทดสอบกับไฟล์ GBPUSD
    test_target_methods("CSV_Files_Fixed/GBPUSD_H1_FIXED.csv")
