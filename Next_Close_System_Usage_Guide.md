# 📖 Next Close System Configuration - คู่มือการใช้งาน

## 🎯 ภาพรวม

ระบบ Next Close System Configuration ได้รับการปรับปรุงให้สามารถใช้งานร่วมกับระบบเก่า (TP/SL System) ได้อย่างสมบูรณ์ พร้อมฟังก์ชันทดสอบ Optimal Parameters ที่เหมาะสม

## 🔧 การตั้งค่าหลัก

### 1. การเปิด/ปิดระบบ

```python
# ใน LightGBM_11_4.py
USE_NEXT_CLOSE_SYSTEM = True            # True = ใช้ Next Close System
USE_MULTI_HORIZON_DECISION = True       # True = ใช้ Multi-Horizon Decision
MULTI_HORIZON_METHOD = '2of3'           # วิธีการตัดสินใจ
```

### 2. การตั้งค่าพารามิเตอร์อัตโนมัติ

ระบบจะปรับพารามิเตอร์อัตโนมัติตามระบบที่เลือก:

**Next Close System:**
- Learning Rate: 0.030 (ลดลงเพื่อรองรับข้อมูลมาก)
- Num Leaves: 40 (เพิ่มขึ้นเพื่อจับ pattern ละเอียด)
- Min Data in Leaf: 15 (เพิ่มขึ้นเพื่อป้องกัน overfitting)
- Regularization: α=0.010, λ=0.010 (เพิ่มขึ้น)

**Traditional System:**
- Learning Rate: 0.060 (มาตรฐาน)
- Num Leaves: 30 (มาตรฐาน)
- Min Data in Leaf: 8 (มาตรฐาน)
- Regularization: α=0.005, λ=0.005 (มาตรฐาน)

## 🎯 ฟังก์ชันใหม่สำหรับ Next Close System

### 1. `find_optimal_threshold_next_close()`

หา threshold ที่เหมาะสมสำหรับ Next Close System:

```python
threshold_results = find_optimal_threshold_next_close(
    trade_df=trade_data,
    symbol="GOLD",
    timeframe="M60",
    horizons=[5, 10, 15],
    method="profit_factor"  # หรือ "win_rate", "expectancy", "sharpe_ratio"
)
```

### 2. `find_optimal_nbars_sl_next_close()`

หา nBars_SL ที่เหมาะสมสำหรับ Next Close System:

```python
nbars_results = find_optimal_nbars_sl_next_close(
    trade_df=trade_data,
    symbol="GOLD",
    timeframe="M60",
    horizons=[5, 10, 15],
    nbars_range=range(2, 11)
)
```

### 3. การบันทึกและโหลดผลลัพธ์

```python
# บันทึกผลลัพธ์
save_next_close_optimal_parameters(
    symbol="GOLD",
    timeframe="M60",
    threshold_results=threshold_results,
    nbars_results=nbars_results
)

# โหลดผลลัพธ์
threshold_results, nbars_results = load_next_close_optimal_parameters("GOLD", "M60")

# ดึงพารามิเตอร์ที่ดีที่สุด
best_params = get_best_next_close_parameters("GOLD", "M60")
```

## 🔄 การใช้งานร่วมกันระหว่างระบบ

### การเปลี่ยนระบบ

1. **เปลี่ยนเป็น Next Close System:**
   ```python
   USE_NEXT_CLOSE_SYSTEM = True
   ```

2. **เปลี่ยนเป็นระบบเก่า:**
   ```python
   USE_NEXT_CLOSE_SYSTEM = False
   ```

3. **รันโปรแกรมใหม่** - ระบบจะปรับพารามิเตอร์อัตโนมัติ

### การทดสอบ Optimal Parameters

ระบบจะเลือกฟังก์ชันทดสอบที่เหมาะสมอัตโนมัติ:

```python
if TEST_OPTIMAL_PARAMETERS:
    if USE_NEXT_CLOSE_SYSTEM:
        # ใช้ฟังก์ชันสำหรับ Next Close System
        threshold_results = find_optimal_threshold_next_close(...)
        nbars_results = find_optimal_nbars_sl_next_close(...)
    else:
        # ใช้ฟังก์ชันสำหรับระบบเก่า
        threshold_results = find_optimal_threshold_multi_model(...)
        nbars_results = find_optimal_nbars_sl_multi_model(...)
```

## 📊 ข้อกำหนดข้อมูล

### Next Close System
- Min Trades per Split: 20
- Min Total Trades: 100
- Relaxed Min Trades: 30

### Traditional System
- Min Trades per Split: 10
- Min Total Trades: 50
- Relaxed Min Trades: 10

## 🧪 การทดสอบระบบ

รันไฟล์ทดสอบเพื่อตรวจสอบการทำงาน:

```bash
python test_next_close_optimization.py
```

### ผลลัพธ์ที่คาดหวัง:
```
✅ Import ฟังก์ชันสำเร็จ
✅ สร้างข้อมูลทดสอบสำเร็จ
✅ ทดสอบ threshold optimization สำเร็จ
✅ ทดสอบ nBars_SL optimization สำเร็จ
✅ ทดสอบการบันทึกและโหลดสำเร็จ
✅ ทดสอบการดึงพารามิเตอร์ที่ดีที่สุดสำเร็จ
🎉 การทดสอบทั้งหมดผ่าน - ระบบพร้อมใช้งาน
```

## 📁 โครงสร้างไฟล์

```
LightGBM/
├── Multi/                              # ระบบหลัก
│   ├── next_close_parameters/          # พารามิเตอร์ Next Close System
│   │   ├── M60_GOLD_next_close_optimal_threshold.pkl
│   │   ├── M60_GOLD_next_close_optimal_nbars.pkl
│   │   ├── M60_GOLD_single_model_next_close_optimal_threshold.pkl
│   │   └── M60_GOLD_single_model_next_close_optimal_nbars.pkl
│   ├── thresholds/                     # พารามิเตอร์ระบบเก่า
│   └── models/                         # โมเดลที่เทรนแล้ว
```

## ⚠️ ข้อควรระวัง

### 1. การใช้หน่วยความจำ
Next Close System ใช้หน่วยความจำมากกว่าระบบเก่า เนื่องจาก:
- สร้างข้อมูลมากกว่า (ทุกแท่งเทียน)
- วิเคราะห์หลาย horizons พร้อมกัน

### 2. เวลาในการประมวลผล
- การเทรนใช้เวลานานขึ้น 20-30%
- การทดสอบ optimal parameters ใช้เวลานานขึ้น

### 3. คุณภาพข้อมูล
ต้องมีข้อมูล `Next_Close_5`, `Next_Close_10`, `Next_Close_15` ครบถ้วน

## 🔍 การแก้ไขปัญหา

### ปัญหา: ไม่มี Next_Close columns
```
❌ ขาดคอลัมน์ที่จำเป็น: ['Next_Close_5', 'Next_Close_10', 'Next_Close_15']
💡 กรุณาเรียก create_features() ก่อนเพื่อสร้าง Next_Close columns
```

**วิธีแก้:** เรียก `create_features()` ก่อนใช้งาน Next Close System

### ปัญหา: ข้อมูลไม่เพียงพอ
```
⚠️ ข้อมูลไม่เพียงพอ (45 trades) - ต้องการอย่างน้อย 100 trades
```

**วิธีแก้:** 
1. ลด `MIN_TOTAL_TRADES` ชั่วคราว
2. หรือเพิ่มข้อมูลการเทรด

### ปัญหา: การทดสอบล้มเหลว
```
❌ เกิดข้อผิดพลาดในการทดสอบ Next Close System
```

**วิธีแก้:**
1. ตรวจสอบ log file ใน `LightGBM/Log_Train.txt`
2. รัน `test_next_close_optimization.py` เพื่อดู error details
3. ตรวจสอบว่ามีไฟล์โมเดลครบถ้วน

## 📈 การปรับแต่งประสิทธิภาพ

### 1. ปรับ Horizons
```python
HORIZONS = [3, 7, 12]  # ปรับตามความต้องการ
```

### 2. ปรับ Method การประเมิน
```python
# ใน find_optimal_threshold_next_close
method="profit_factor"  # หรือ "win_rate", "expectancy", "sharpe_ratio"
```

### 3. ปรับช่วงการทดสอบ
```python
# ใน find_optimal_nbars_sl_next_close
nbars_range=range(2, 15)  # ขยายช่วงการทดสอบ
```

## 🎯 แนวทางการใช้งาน

### สำหรับการพัฒนา (Development):
```python
DEVELOPMENT_MODE = True
TEST_OPTIMAL_PARAMETERS = True
USE_NEXT_CLOSE_SYSTEM = True
```

### สำหรับการใช้งานจริง (Production):
```python
DEVELOPMENT_MODE = False
TEST_OPTIMAL_PARAMETERS = False
USE_NEXT_CLOSE_SYSTEM = True  # หรือ False ตามต้องการ
```

---

## 📞 การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ log files
2. รัน test script
3. ตรวจสอบการตั้งค่าพารามิเตอร์
4. ตรวจสอบความครบถ้วนของข้อมูล
