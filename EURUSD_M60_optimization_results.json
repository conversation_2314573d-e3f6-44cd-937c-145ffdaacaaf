{"optimization_info": {"symbol": "EURUSD", "timeframe": "M60", "source": "EURUSD_M60 specific optimization", "score": 47.8, "win_rate": 30.1, "total_profit": 2240, "total_trades": 89, "expectancy": 25.17, "max_drawdown": 1680, "optimization_date": "2025-09-28", "test_period": "Historical backtest data"}, "parameters": {"input_stop_loss_atr": 1.15, "input_take_profit": 2.3, "input_rsi_level_in": 37, "input_volume_spike": 1.28, "input_rsi_level_over": 70, "input_rsi_level_out": 33, "input_pull_back": 0.44, "input_initial_nbar_sl": 5}, "parameter_changes": {}, "performance_metrics": {"before_optimization": {"win_rate": 0.0, "total_profit": 0, "expectancy": 0.0, "max_drawdown": 0}, "after_optimization": {"win_rate": 30.1, "total_profit": 2240, "expectancy": 25.17, "max_drawdown": 1680}, "improvement": {"win_rate_improvement": 0.0, "profit_improvement": 0, "expectancy_improvement": 0.0, "drawdown_reduction": 0}}, "validation": {"cross_validation_score": 0.7966666666666666, "out_of_sample_performance": 0.86, "parameter_stability": "Medium", "recommended_for_live_trading": true}}