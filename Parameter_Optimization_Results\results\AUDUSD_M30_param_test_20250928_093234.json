{"metadata": {"symbol": "AUDUSD", "timeframe": "M30", "test_timestamp": "20250928_093234", "performance_score": 0}, "parameters": {"input_stop_loss_atr": 1.0, "input_take_profit": 2.0, "input_rsi_level_in": 25, "input_volume_spike": 1.0}, "trade_results": {"total_profit": -29.049999999999358, "win_rate": 25.0, "total_trades": 8, "max_drawdown": 18.000000000000238, "profit_factor": 0.2625566636446195, "expectancy": -3.6312499999999197, "trades": [{"entry_time": "2020.03.10", "exit_time": "2020.03.10", "entry_price": 0.66014, "exit_price": 0.6583707142857143, "profit": -0.0017692857142856955, "profit_pips": -17.692857142856955, "exit_condition": "SL Hit"}, {"entry_time": "2020.07.15", "exit_time": "2020.07.16", "entry_price": 0.70055, "exit_price": 0.69967, "profit": -0.0008799999999999919, "profit_pips": -8.799999999999919, "exit_condition": "SL Hit"}, {"entry_time": "2021.09.02", "exit_time": "2021.09.02", "entry_price": 0.73676, "exit_price": 0.73654, "profit": -0.00021999999999999797, "profit_pips": -2.1999999999999797, "exit_condition": "Technical Exit"}, {"entry_time": "2022.08.09", "exit_time": "2022.08.09", "entry_price": 0.69814, "exit_price": 0.69824, "profit": 9.999999999998899e-05, "profit_pips": 0.9999999999998899, "exit_condition": "Technical Exit"}, {"entry_time": "2023.04.21", "exit_time": "2023.04.21", "entry_price": 0.67411, "exit_price": 0.67358, "profit": -0.0005300000000000304, "profit_pips": -5.3000000000003045, "exit_condition": "Technical Exit"}, {"entry_time": "2023.08.01", "exit_time": "2023.08.01", "entry_price": 0.67158, "exit_price": 0.67131, "profit": -0.00026999999999999247, "profit_pips": -2.6999999999999247, "exit_condition": "Technical Exit"}, {"entry_time": "2024.07.04", "exit_time": "2024.07.04", "entry_price": 0.67028, "exit_price": 0.6712142857142858, "profit": 0.0009342857142857763, "profit_pips": 9.342857142857763, "exit_condition": "TP Hit"}, {"entry_time": "2025.01.24", "exit_time": "2025.01.24", "entry_price": 0.62824, "exit_price": 0.62797, "profit": -0.00026999999999999247, "profit_pips": -2.6999999999999247, "exit_condition": "Technical Exit"}], "winning_trades": 2, "losing_trades": 6, "gross_profit": 10.342857142857653, "gross_loss": 39.392857142857004}, "scoring_details": {"weights_used": {"total_profit": 0.3, "win_rate": 0.25, "profit_factor": 0.2, "max_drawdown": -0.15, "expectancy": 0.1}, "score_calculation": "Weighted average of normalized metrics"}}