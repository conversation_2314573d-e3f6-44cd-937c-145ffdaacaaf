// ช่วยแก้ไข code LightGBM_11_2.py

// จาก code เดิมมักจะมีปันหาของจำนวนข้อมูลที่ไม่เพียงพอ เพื่อใช้ในการเทรนโมเดล จึงต้องการแก้ไขปันหา
ฉันจึงมีความคิด เช่น
ปัจจุบันเป็นการซื้อ-ขาย หรือเข้า-ออก เป็นรอบเช่น เข้าซื้อ-แล้วรอราคามาชน TP SL จึงทำให้มีจำนวนข้อมูลที่น้อย
ฉันจึงเสนอแนวทางการแก้ไขโดยใช้ next_close มาช่วยแทน เช่นกำหนดช่วงเวลา เป็นระยะสั้น-กลาง-ยาว อาจใช้ 5, 10, 15 แท่ง (ช่วยแนะนำฉันอีกครั้ง)
แต่ยังคงพื้นฐานแบ่งแยกตามสภาวะตลาดออกเป็น 6 senario เหมือนเดิม
counter_trend, counter_trend_Buy, counter_trend_Sell, trend_following, trend_following_Buy, trend_following_Sell
และ Target ยังคงเหมือนเดิม Main_Target, Target_Buy, Target_Sell
ช่วยแนะนำการจัดการระบบให้มีการจัดการที่ดี และเรียบง่าย

เช่น
เมื่อราคา close มากกว่า ema200 สร้าง trend_following, trend_following_Buy, counter_trend, counter_trend_Sell ไปพร้อมๆกัน
เมื่อราคา close น้อยกว่า ema200 สร้าง trend_following, trend_following_Sell, counter_trend, counter_trend_Buy ไปพร้อมๆกัน
เพื่อให้มีข้อมูลที่มากพอในการเทรนโมเดล

และช่วยปรับปรุงตอนการสร้างข้อมูลเพื่อนำไปเทรน และระบบขั้นตอนการเทรนโมเดล และการนำไปใช้งาน
แต่เนื่องจากต้องการวิเคราะห์ สั้น-กลาง-ยาว จะมีการจัดการอย่างไรเพื่อตัดสินใจอย่างไร

// ขั้นตอน process_trade_targets()
จากการเรียกใช้
trade_df = process_trade_targets(trade_df, symbol, timeframe)
เนื่องจาก horizon = 5
df['Next_Close_5'] = df['Close'].shift(-horizon) << df['Close'] ได้จากการดึงจาก trade_df ไม่ใช้ df
เนื่องจาก trade_df คือช่วงเวลาที่ดึงมา เวลาไม่ได้ต่อเนื่องกัน คิดว่าการได้ข้อมูล close ไม่ถูกต้อง
ถ้าต้องการแก้ไขคงต้องสร้าง df['Next_Close_5'] ตั้งแต่ขั้นตอนการสร้าง features แล้วค่อยลบออกเพื่อป้องกัน ข้อมูลอนาคตก่อนเทรน

// จากข้อคำถามทั้งหมด ช่วยวิเคราะห์ และตรวจสอบทั้งระบบ และลำดับขั้นตอนการทำงาน และเลือกวิธีการที่เหมาะสม และข้อไหนคิดว่ามีวิธีที่ดีกว่าก็ช่วยเสนอให้ฉันด้วย..ขอบคุณ

// แนวทางเบื้องต้นที่ฉันคิดไว้...ช่วยปรับแก้ให้ใช้งานได้กับ code
ขั้นตอน create_features() เพิ่มเติม feature เช่น
    df['Next_Close_5'] = df['Close'].shift(-5)
    df['Next_Close_10'] = df['Close'].shift(-10)
    df['Next_Close_15'] = df['Close'].shift(-15)
...และช่วยตรวจสอบต้องเพิ่มตรงไหนอีก
ขั้นตอน load_and_process_data() > try_trade_with_threshold_adjustment() > create_trade_cycles_with_model()
ช่วยตรวจสอบการใช้งานของ buy และ sell เนื่องจากตรงนี้ไม่ได้ ซื้อ-ขาย เป็นรอบแล้วแต่เข้าซื้อทุกแท่ง เช่น 
กรณี buy เมื่อ close>ema200 ต้องสร้าง "trend_following" เมื่อ close>open และ "counter_trend" เมื่อ close<open ของแท่งก่อนหน้า
ดังนั้นวิธีนี้จะไม่ใช้ sl และ tp จะไม่ใช้ exit_condition จึงต้องปรับใหม่ที่เกี่ยวข้อง
entry_price_buy = df["Open"].iloc[i] + (symbol_spread * symbol_points) << เหมือนเดิม
profit = (df['Next_Close_5'] - entry_price_buy) / symbol_points < แต่เนื่องจากต้องการวิเคราะห์ สั้น-กลาง-ยาว profit จึงจะมี 3 แบบ
ส่วน sell ก็ทำคล้ายกัน ถ้าแบบนี้จะมีข้อมูลมากพอในการเทรนโมเดล
แต่เมื่อทดสอบอีกครั้ง...ช่วยออกแบบการที่ให้โมเดลช่วยตัดสินใน เช่น เมื่อมีการยืนยันทิศทางเดียวกันทั้ง 3 ระดับ ถึงจะให้เป็น buy หรือ sell
หรือ 2 ใน 3 อาจเป็นทางเลือกอีกทาง

   

