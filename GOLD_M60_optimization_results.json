{"optimization_info": {"symbol": "GOLD", "timeframe": "M60", "source": "GOLD_M60 specific optimization", "score": 52.3, "win_rate": 31.5, "total_profit": 22800, "total_trades": 98, "expectancy": 232.65, "max_drawdown": 18900, "optimization_date": "2025-09-28", "test_period": "Historical backtest data"}, "parameters": {"input_stop_loss_atr": 1.1, "input_take_profit": 2.2, "input_rsi_level_in": 36, "input_volume_spike": 1.2, "input_rsi_level_over": 70, "input_rsi_level_out": 34, "input_pull_back": 0.46, "input_initial_nbar_sl": 4}, "parameter_changes": {}, "performance_metrics": {"before_optimization": {"win_rate": 0.0, "total_profit": 0, "expectancy": 0.0, "max_drawdown": 0}, "after_optimization": {"win_rate": 31.5, "total_profit": 22800, "expectancy": 232.65, "max_drawdown": 18900}, "improvement": {"win_rate_improvement": 0.0, "profit_improvement": 0, "expectancy_improvement": 0.0, "drawdown_reduction": 0}}, "validation": {"cross_validation_score": 0.8716666666666666, "out_of_sample_performance": 0.9, "parameter_stability": "High", "recommended_for_live_trading": true}}