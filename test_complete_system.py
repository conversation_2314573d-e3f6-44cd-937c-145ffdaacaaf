#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Complete System Test
======================

สคริปต์สำหรับทดสอบระบบ Parameter Optimization Integration ทั้งหมด
"""

import os
import sys
import time
import json
import requests
import subprocess
from datetime import datetime

# การตั้งค่า
SERVER_HOST = "127.0.0.1"
SERVER_PORT = 54321
BASE_URL = f"http://{SERVER_HOST}:{SERVER_PORT}"

def check_file_exists(filename):
    """ตรวจสอบว่าไฟล์มีอยู่หรือไม่"""
    exists = os.path.exists(filename)
    status = "✅" if exists else "❌"
    print(f"   {status} {filename}")
    return exists

def test_file_structure():
    """ทดสอบโครงสร้างไฟล์"""
    print(f"\n📁 Testing File Structure")
    print(f"=" * 40)
    
    required_files = [
        "WebRequest_Server_06.py",
        "USDJPY_M60_optimization_results.json",
        "test_parameter_endpoints.py",
        "create_optimization_results.py",
        "validate_optimization_files.py"
    ]
    
    all_exist = True
    for file in required_files:
        if not check_file_exists(file):
            all_exist = False
    
    if all_exist:
        print(f"✅ All required files exist")
    else:
        print(f"❌ Some files are missing")
    
    return all_exist

def test_optimization_files():
    """ทดสอบไฟล์ optimization results"""
    print(f"\n🎯 Testing Optimization Results Files")
    print(f"=" * 40)
    
    try:
        # รัน validation script
        result = subprocess.run([
            sys.executable, "validate_optimization_files.py"
        ], capture_output=True, text=True, input="1\n3\n", timeout=30)
        
        if result.returncode == 0:
            print(f"✅ Optimization files validation passed")
            return True
        else:
            print(f"❌ Optimization files validation failed")
            print(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⚠️ Validation script timeout")
        return False
    except Exception as e:
        print(f"❌ Error running validation: {e}")
        return False

def test_server_startup():
    """ทดสอบการเริ่มต้น server"""
    print(f"\n🚀 Testing Server Startup")
    print(f"=" * 40)
    
    try:
        # ทดสอบการเชื่อมต่อ
        response = requests.get(f"{BASE_URL}/get_parameters", timeout=5)
        if response.status_code == 200:
            print(f"✅ Server is running and responding")
            return True
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to server at {BASE_URL}")
        print(f"💡 Please start WebRequest_Server_06.py manually")
        return False
    except Exception as e:
        print(f"❌ Server connection error: {e}")
        return False

def test_parameter_loading():
    """ทดสอบการโหลดพารามิเตอร์"""
    print(f"\n📊 Testing Parameter Loading")
    print(f"=" * 40)
    
    try:
        # ทดสอบ get_parameters
        response = requests.get(f"{BASE_URL}/get_parameters", timeout=10)
        if response.status_code == 200:
            data = response.json()
            params = data.get("parameters", {})
            
            # ตรวจสอบพารามิเตอร์สำคัญ
            expected_params = [
                "input_stop_loss_atr", "input_take_profit", 
                "input_rsi_level_in", "input_volume_spike"
            ]
            
            missing_params = []
            for param in expected_params:
                if param not in params:
                    missing_params.append(param)
            
            if not missing_params:
                print(f"✅ All expected parameters loaded")
                print(f"   📊 Parameters:")
                for param in expected_params:
                    print(f"      {param}: {params[param]}")
                return True
            else:
                print(f"❌ Missing parameters: {missing_params}")
                return False
        else:
            print(f"❌ Failed to get parameters: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Parameter loading test failed: {e}")
        return False

def test_parameter_update():
    """ทดสอบการอัปเดตพารามิเตอร์"""
    print(f"\n🔄 Testing Parameter Update")
    print(f"=" * 40)
    
    try:
        # ทดสอบ update_parameters
        payload = {"symbol": "USDJPY", "timeframe": "M60"}
        response = requests.post(
            f"{BASE_URL}/update_parameters",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print(f"✅ Parameter update successful")
                updated_params = data.get("updated_parameters", {})
                print(f"   📊 Updated parameters count: {len(updated_params)}")
                return True
            else:
                print(f"❌ Parameter update failed: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ Parameter update failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Parameter update test failed: {e}")
        return False

def test_parameter_status():
    """ทดสอบการดูสถานะพารามิเตอร์"""
    print(f"\n📋 Testing Parameter Status")
    print(f"=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/parameter_status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print(f"✅ Parameter status retrieved successfully")
                
                optimization_available = data.get("optimization_file_available", False)
                print(f"   📁 Optimization file available: {optimization_available}")
                
                current_params = data.get("current_parameters", {})
                print(f"   📊 Current parameters count: {len(current_params)}")
                
                return True
            else:
                print(f"❌ Parameter status failed: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ Parameter status failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Parameter status test failed: {e}")
        return False

def run_complete_test():
    """รันการทดสอบทั้งหมด"""
    print(f"🧪 Complete System Test Suite")
    print(f"=" * 60)
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Target server: {BASE_URL}")
    
    tests = [
        ("File Structure", test_file_structure),
        ("Optimization Files", test_optimization_files),
        ("Server Startup", test_server_startup),
        ("Parameter Loading", test_parameter_loading),
        ("Parameter Update", test_parameter_update),
        ("Parameter Status", test_parameter_status)
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n" + "="*60)
        print(f"🧪 Running Test: {test_name}")
        
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            results[test_name] = False
            print(f"❌ {test_name}: ERROR - {e}")
        
        time.sleep(1)  # หน่วงเวลาระหว่างการทดสอบ
    
    # สรุปผล
    print(f"\n" + "="*60)
    print(f"📊 Test Results Summary")
    print(f"=" * 60)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n📈 Overall Results:")
    print(f"   ✅ Passed: {passed}/{total}")
    print(f"   ❌ Failed: {total - passed}/{total}")
    print(f"   📊 Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print(f"\n🎉 All tests passed! System is ready for use.")
        print(f"\n💡 Next steps:")
        print(f"   1. Start WebRequest_Server_06.py")
        print(f"   2. Test with MT5 EA integration")
        print(f"   3. Monitor parameter optimization results")
    else:
        print(f"\n⚠️ Some tests failed. Please review and fix issues.")
        print(f"\n🔧 Troubleshooting:")
        print(f"   1. Check if all required files exist")
        print(f"   2. Ensure WebRequest_Server_06.py is running")
        print(f"   3. Validate optimization results files")
        print(f"   4. Check server logs for errors")
    
    print(f"\n🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed == total

def create_test_report():
    """สร้างรายงานการทดสอบ"""
    report = {
        "test_info": {
            "timestamp": datetime.now().isoformat(),
            "server_url": BASE_URL,
            "test_suite": "Complete System Test"
        },
        "results": {}
    }
    
    # รันการทดสอบและเก็บผล
    tests = [
        ("file_structure", test_file_structure),
        ("optimization_files", test_optimization_files),
        ("server_startup", test_server_startup),
        ("parameter_loading", test_parameter_loading),
        ("parameter_update", test_parameter_update),
        ("parameter_status", test_parameter_status)
    ]
    
    for test_key, test_func in tests:
        try:
            result = test_func()
            report["results"][test_key] = {
                "status": "passed" if result else "failed",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            report["results"][test_key] = {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    # บันทึกรายงาน
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Test report saved to: {report_file}")
    return report_file

def main():
    """ฟังก์ชันหลัก"""
    print(f"🧪 Complete System Tester")
    print(f"=" * 50)
    
    while True:
        print(f"\n📋 Select test mode:")
        print(f"1. 🚀 Run complete test suite")
        print(f"2. 📄 Generate test report")
        print(f"3. 🔍 Individual test selection")
        print(f"4. 🚪 Exit")
        
        choice = input(f"\nChoice (1-4): ").strip()
        
        if choice == '1':
            run_complete_test()
        elif choice == '2':
            create_test_report()
        elif choice == '3':
            print(f"🔧 Individual test mode not implemented yet")
            print(f"💡 Use option 1 for complete testing")
        elif choice == '4':
            print(f"👋 Goodbye!")
            break
        else:
            print(f"❌ Invalid choice. Please select 1-4.")

if __name__ == "__main__":
    main()
