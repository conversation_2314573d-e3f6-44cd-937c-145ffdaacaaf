#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Validate Optimization Results Files
=====================================

สคริปต์สำหรับตรวจสอบและ validate ไฟล์ optimization results
"""

import json
import os
import glob
from datetime import datetime

def validate_optimization_file(filepath):
    """
    ตรวจสอบไฟล์ optimization results
    
    Args:
        filepath: path ของไฟล์ที่ต้องการตรวจสอบ
    
    Returns:
        dict: ผลการตรวจสอบ
    """
    result = {
        "file": filepath,
        "valid": False,
        "errors": [],
        "warnings": [],
        "info": {}
    }
    
    try:
        # ตรวจสอบว่าไฟล์มีอยู่
        if not os.path.exists(filepath):
            result["errors"].append("File does not exist")
            return result
        
        # โหลดไฟล์ JSON
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # ตรวจสอบโครงสร้างหลัก
        required_sections = ["optimization_info", "parameters"]
        for section in required_sections:
            if section not in data:
                result["errors"].append(f"Missing required section: {section}")
        
        # ตรวจสอบ optimization_info
        if "optimization_info" in data:
            info = data["optimization_info"]
            required_info_fields = ["symbol", "timeframe", "score", "win_rate"]
            
            for field in required_info_fields:
                if field not in info:
                    result["errors"].append(f"Missing optimization_info field: {field}")
                else:
                    result["info"][field] = info[field]
        
        # ตรวจสอบ parameters
        if "parameters" in data:
            params = data["parameters"]
            required_params = [
                "input_stop_loss_atr", "input_take_profit", 
                "input_rsi_level_in", "input_volume_spike"
            ]
            
            for param in required_params:
                if param not in params:
                    result["errors"].append(f"Missing parameter: {param}")
                else:
                    # ตรวจสอบค่าที่สมเหตุสมผล
                    value = params[param]
                    if param == "input_stop_loss_atr" and (value < 0.5 or value > 3.0):
                        result["warnings"].append(f"{param} value {value} seems unusual (expected 0.5-3.0)")
                    elif param == "input_take_profit" and (value < 1.0 or value > 5.0):
                        result["warnings"].append(f"{param} value {value} seems unusual (expected 1.0-5.0)")
                    elif param == "input_rsi_level_in" and (value < 20 or value > 50):
                        result["warnings"].append(f"{param} value {value} seems unusual (expected 20-50)")
                    elif param == "input_volume_spike" and (value < 1.0 or value > 2.0):
                        result["warnings"].append(f"{param} value {value} seems unusual (expected 1.0-2.0)")
        
        # ตรวจสอบ performance metrics
        if "performance_metrics" in data:
            metrics = data["performance_metrics"]
            if "after_optimization" in metrics:
                after = metrics["after_optimization"]
                if "win_rate" in after and after["win_rate"] < 20:
                    result["warnings"].append(f"Low win rate: {after['win_rate']}%")
                if "expectancy" in after and after["expectancy"] < 0:
                    result["warnings"].append(f"Negative expectancy: {after['expectancy']}")
        
        # ถ้าไม่มี error ถือว่า valid
        if not result["errors"]:
            result["valid"] = True
        
    except json.JSONDecodeError as e:
        result["errors"].append(f"Invalid JSON format: {str(e)}")
    except Exception as e:
        result["errors"].append(f"Unexpected error: {str(e)}")
    
    return result

def find_optimization_files():
    """ค้นหาไฟล์ optimization results ทั้งหมด"""
    patterns = [
        "*optimization_results*.json",
        "*_optimization_*.json",
        "baseline_result.json",
        "*parameter*.json"
    ]
    
    files = []
    for pattern in patterns:
        files.extend(glob.glob(pattern))
    
    # ลบ duplicate
    return list(set(files))

def validate_all_files():
    """ตรวจสอบไฟล์ทั้งหมด"""
    files = find_optimization_files()
    
    if not files:
        print("❌ No optimization results files found")
        print("💡 Run create_optimization_results.py to create sample files")
        return
    
    print(f"🔍 Found {len(files)} optimization files to validate")
    print("=" * 60)
    
    valid_files = 0
    total_errors = 0
    total_warnings = 0
    
    for filepath in files:
        print(f"\n📁 Validating: {filepath}")
        print("-" * 40)
        
        result = validate_optimization_file(filepath)
        
        if result["valid"]:
            print(f"✅ Valid")
            valid_files += 1
            
            # แสดงข้อมูลสำคัญ
            if result["info"]:
                info = result["info"]
                symbol = info.get("symbol", "N/A")
                timeframe = info.get("timeframe", "N/A")
                score = info.get("score", "N/A")
                win_rate = info.get("win_rate", "N/A")
                print(f"   📊 {symbol} {timeframe} - Score: {score}, Win Rate: {win_rate}%")
        else:
            print(f"❌ Invalid")
        
        # แสดง errors
        if result["errors"]:
            total_errors += len(result["errors"])
            print(f"   🚨 Errors ({len(result['errors'])}):")
            for error in result["errors"]:
                print(f"      - {error}")
        
        # แสดง warnings
        if result["warnings"]:
            total_warnings += len(result["warnings"])
            print(f"   ⚠️ Warnings ({len(result['warnings'])}):")
            for warning in result["warnings"]:
                print(f"      - {warning}")
    
    # สรุปผล
    print(f"\n" + "=" * 60)
    print(f"📊 Validation Summary:")
    print(f"   📁 Total files: {len(files)}")
    print(f"   ✅ Valid files: {valid_files}")
    print(f"   ❌ Invalid files: {len(files) - valid_files}")
    print(f"   🚨 Total errors: {total_errors}")
    print(f"   ⚠️ Total warnings: {total_warnings}")
    
    if valid_files == len(files) and total_errors == 0:
        print(f"\n🎉 All files are valid!")
    elif valid_files > 0:
        print(f"\n✅ {valid_files} files are ready to use")
        if total_warnings > 0:
            print(f"⚠️ Please review warnings for potential issues")
    else:
        print(f"\n❌ No valid files found. Please fix errors and try again.")

def validate_specific_file():
    """ตรวจสอบไฟล์เฉพาะ"""
    files = find_optimization_files()
    
    if not files:
        print("❌ No optimization results files found")
        return
    
    print(f"📁 Available files:")
    for i, file in enumerate(files, 1):
        print(f"   {i}. {file}")
    
    try:
        choice = int(input(f"\nSelect file to validate (1-{len(files)}): ")) - 1
        if 0 <= choice < len(files):
            filepath = files[choice]
            print(f"\n🔍 Validating: {filepath}")
            print("=" * 50)
            
            result = validate_optimization_file(filepath)
            
            if result["valid"]:
                print(f"✅ File is valid!")
            else:
                print(f"❌ File has issues:")
            
            if result["errors"]:
                print(f"\n🚨 Errors:")
                for error in result["errors"]:
                    print(f"   - {error}")
            
            if result["warnings"]:
                print(f"\n⚠️ Warnings:")
                for warning in result["warnings"]:
                    print(f"   - {warning}")
            
            if result["info"]:
                print(f"\n📊 File Info:")
                for key, value in result["info"].items():
                    print(f"   {key}: {value}")
        else:
            print("❌ Invalid selection")
    except ValueError:
        print("❌ Please enter a valid number")

def main():
    """ฟังก์ชันหลัก"""
    print(f"🔍 Optimization Results File Validator")
    print(f"=" * 50)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    while True:
        print(f"\n📋 Select validation mode:")
        print(f"1. 🔍 Validate all files")
        print(f"2. 📁 Validate specific file")
        print(f"3. 🚪 Exit")
        
        choice = input(f"\nChoice (1-3): ").strip()
        
        if choice == '1':
            validate_all_files()
        elif choice == '2':
            validate_specific_file()
        elif choice == '3':
            print(f"👋 Goodbye!")
            break
        else:
            print(f"❌ Invalid choice. Please select 1-3.")

if __name__ == "__main__":
    main()
