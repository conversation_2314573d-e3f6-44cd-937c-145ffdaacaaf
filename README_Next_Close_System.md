# 📊 Next Close System - คู่มือการใช้งาน

## 🎯 ภาพรวมระบบใหม่

ระบบ **Next Close System** เป็นการปรับปรุงจากระบบเดิมที่รอ TP/SL โดยใช้ `next_close` ในระยะเวลาต่างๆ แทน เพื่อ:

- ✅ **เพิ่มจำนวนข้อมูล** สำหรับการเทรนโมเดล
- ✅ **แก้ไขปัญหาข้อมูลไม่ต่อเนื่อง** ใน process_trade_targets
- ✅ **วิเคราะห์หลายระยะเวลา** (สั้น-กลาง-ยาว) พร้อมกัน
- ✅ **ระบบตัดสินใจแบบ Multi-Horizon** (2 ใน 3, ทั้ง 3, majority)

## 🔧 การตั้งค่าระบบ

### 1. เปิดใช้งานระบบใหม่

ใน `LightGBM_11_2.py` ปรับการตั้งค่าดังนี้:

```python
# Next Close System Configuration (ระบบใหม่)
USE_NEXT_CLOSE_SYSTEM = True            # ใช้ระบบ Next Close แทนการรอ TP/SL
USE_MULTI_HORIZON_DECISION = True       # ใช้ระบบตัดสินใจแบบ Multi-Horizon
MULTI_HORIZON_METHOD = '2of3'           # วิธีการตัดสินใจ: '2of3', 'all3', 'majority'

# Horizons Configuration
HORIZONS = [5, 10, 15]  # ระยะสั้น, กลาง, ยาว
```

### 2. Market Scenarios ใหม่

ระบบใหม่ใช้ 4 scenarios แทน 6 scenarios เดิม:

```python
MARKET_SCENARIOS = {
    'trend_following_buy': {
        'condition': lambda row: row['Close'] > row['EMA200'] and row['Close'] > row['Open'],
        'action': 'buy'
    },
    'trend_following_sell': {
        'condition': lambda row: row['Close'] < row['EMA200'] and row['Close'] < row['Open'],
        'action': 'sell'
    },
    'counter_trend_buy': {
        'condition': lambda row: row['Close'] < row['EMA200'] and row['Close'] > row['Open'],
        'action': 'buy'
    },
    'counter_trend_sell': {
        'condition': lambda row: row['Close'] > row['EMA200'] and row['Close'] < row['Open'],
        'action': 'sell'
    }
}
```

## 🚀 ฟังก์ชันหลักใหม่

### 1. `create_features()` - ปรับปรุงแล้ว

เพิ่ม Next_Close features ตั้งแต่ขั้นตอนแรก:

```python
def create_features(df, symbol, timeframe, nBars_SL):
    # เพิ่ม Next_Close Features สำหรับ Multi-Horizon Analysis
    df['Next_Close_5'] = df['Close'].shift(-5)    # ระยะสั้น
    df['Next_Close_10'] = df['Close'].shift(-10)  # ระยะกลาง  
    df['Next_Close_15'] = df['Close'].shift(-15)  # ระยะยาว
    
    # คำนวณ Price Change Percentage
    df['Price_Change_5'] = ((df['Next_Close_5'] - df['Close']) / df['Close']) * 100
    df['Price_Change_10'] = ((df['Next_Close_10'] - df['Close']) / df['Close']) * 100
    df['Price_Change_15'] = ((df['Next_Close_15'] - df['Close']) / df['Close']) * 100
    
    # ... ส่วนอื่นๆ เหมือนเดิม
```

### 2. `create_trade_cycles_with_next_close()` - ใหม่

สร้าง trade cycles ด้วย next_close แทนการรอ TP/SL:

```python
trade_df, stats = create_trade_cycles_with_next_close(
    df, scenario_models, model_features,
    symbol, timeframe, model_confidence_threshold,
    entry_condition_func, entry_condition_name, 
    horizons=[5, 10, 15]
)
```

**ผลลัพธ์:**
- สร้าง trades สำหรับทุก scenario และ horizon
- คำนวณ profit จาก next_close โดยตรง
- ไม่ต้องรอ TP/SL hit

### 3. `create_multi_horizon_decision_system()` - ใหม่

ระบบตัดสินใจจากการวิเคราะห์หลายระยะเวลา:

```python
trade_df, stats, decision_summary = create_multi_horizon_decision_system(
    df, scenario_models, model_features,
    symbol, timeframe, model_confidence_threshold,
    horizons=[5, 10, 15],
    decision_method='2of3'  # '2of3', 'all3', 'majority'
)
```

**วิธีการตัดสินใจ:**
- `'2of3'`: ต้องการ 2 ใน 3 horizons เห็นด้วย
- `'all3'`: ต้องการทุก horizons เห็นด้วย
- `'majority'`: ใช้ majority vote

### 4. `process_trade_targets_next_close()` - ใหม่

สร้าง targets สำหรับ Multi-Horizon Analysis:

```python
df_with_targets = process_trade_targets_next_close(
    trade_df, symbol, timeframe, 
    horizons=[5, 10, 15]
)
```

**Targets ที่สร้าง:**
- `Target_H5`, `Target_H10`, `Target_H15`: Binary targets สำหรับแต่ละ horizon
- `Target_2of3`: Combined target (2 ใน 3 horizons เห็นด้วย)
- `Target_Multiclass`: Multi-class target จาก profit thresholds
- `Target_{scenario}`: Binary targets สำหรับแต่ละ scenario

## 🧪 การทดสอบระบบ

### 1. ทดสอบด้วยไฟล์ test

```bash
python test_next_close_system.py
```

### 2. ทดสอบใน LightGBM_11_2.py

```python
# ตั้งค่าการทดสอบ
USE_NEXT_CLOSE_SYSTEM = True
USE_MULTI_HORIZON_DECISION = True

# รันโปรแกรม
python LightGBM_11_2.py
```

## 📊 ข้อมูลที่ได้จากระบบใหม่

### 1. Trade Data

```python
# ตัวอย่าง trade record
{
    'Entry Time': '2024-01-01 10:00:00',
    'Entry Price': 1.3000,
    'Exit Price': 1.3015,  # จาก Next_Close_5
    'Profit': 15.0,
    'Trade Type': 'Buy',
    'Scenario': 'trend_following_buy',
    'Horizon': 5,
    'Horizon_Name': 'short',
    'Strategy': 'momentum_buy',
    'Market_Type': 'trend_following'
}
```

### 2. Decision Summary

```python
{
    'method': '2of3',
    'decisions': {
        'trend_following_buy': {
            'final_decision': 1,  # 1=TRADE, 0=HOLD
            'votes': [1, 1, 0],   # votes จาก 3 horizons
            'vote_summary': '2/3'
        }
    },
    'confidence_scores': {
        'trend_following_buy': 0.75
    }
}
```

### 3. Statistics

```python
{
    'summary': {
        'total_trades': 1500,
        'win_rate': 0.58,
        'total_profit': 2500.0,
        'expectancy': 1.67,
        'profit_factor': 1.45
    },
    'by_scenario': {...},
    'by_horizon': {...}
}
```

## 🔄 การเปรียบเทียบระบบเดิมและใหม่

| ด้าน | ระบบเดิม | ระบบใหม่ |
|------|----------|----------|
| **จำนวนข้อมูล** | น้อย (รอ TP/SL) | มาก (ทุกแท่ง) |
| **ความต่อเนื่อง** | ไม่ต่อเนื่อง | ต่อเนื่อง |
| **การวิเคราะห์** | 1 ระยะเวลา | 3 ระยะเวลา |
| **การตัดสินใจ** | แบบเดียว | Multi-Horizon |
| **Scenarios** | 6 แบบ | 4 แบบ (เพิ่มข้อมูล) |
| **Target Types** | 3 แบบ | 7+ แบบ |

## ⚠️ ข้อควรระวัง

1. **Memory Usage**: ระบบใหม่ใช้ memory มากกว่าเดิม
2. **Processing Time**: การประมวลผลใช้เวลานานขึ้น
3. **Data Quality**: ต้องมีข้อมูล Next_Close ครบถ้วน
4. **Model Compatibility**: อาจต้องปรับ model parameters

## 🎯 แนวทางการใช้งาน

### สำหรับการทดสอบ:
```python
USE_NEXT_CLOSE_SYSTEM = True
USE_MULTI_HORIZON_DECISION = False  # ใช้แบบธรรมดาก่อน
```

### สำหรับการใช้งานจริง:
```python
USE_NEXT_CLOSE_SYSTEM = True
USE_MULTI_HORIZON_DECISION = True
MULTI_HORIZON_METHOD = '2of3'  # สมดุลระหว่างความแม่นยำและโอกาส
```

### สำหรับการเปรียบเทียบ:
```python
USE_NEXT_CLOSE_SYSTEM = False  # ใช้ระบบเดิม
# หรือ
USE_NEXT_CLOSE_SYSTEM = True   # ใช้ระบบใหม่
```

## 📞 การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ log files ใน `LightGBM/Log_Train.txt`
2. รัน `test_next_close_system.py` เพื่อทดสอบ
3. ตรวจสอบการตั้งค่า configuration
4. ดู decision summary files ที่บันทึกไว้
