
# ==============================================
# Model Protection System
# ==============================================

import os
import json
import shutil
from datetime import datetime

class ModelProtectionSystem:
    def __init__(self, min_profit_threshold=100):
        self.min_profit_threshold = min_profit_threshold
        self.protection_log = "model_protection.log"
        
    def should_save_model(self, current_performance, symbol, timeframe):
        """ตัดสินใจว่าควรบันทึกโมเดลหรือไม่"""

        print(f"🔍 Model Protection: ตรวจสอบโมเดล {symbol}_{timeframe}")

        current_profit = current_performance.get('total_profit', 0)
        current_win_rate = current_performance.get('win_rate', 0)
        current_expectancy = current_performance.get('expectancy', 0)

        print(f"📊 โมเดลปัจจุบัน:")
        print(f"   💰 Total Profit: ${current_profit:,.2f}")
        print(f"   🎯 Win Rate: {current_win_rate:.1%}")
        print(f"   📈 Expectancy: {current_expectancy:.2f}")

        # โหลดประสิทธิภาพโมเดลปัจจุบัน
        best_performance = self._load_best_performance(symbol, timeframe)

        if best_performance is None:
            print("🔍 ไม่มีโมเดลก่อนหน้า - ตรวจสอบคุณภาพโมเดลแรก")

            # ตรวจสอบคุณภาพโมเดลแรกก่อนบันทึก
            if current_profit < 0:
                print(f"❌ โมเดลแรกขาดทุน (${current_profit:,.0f}) - ไม่บันทึก")
                return False, "first_model_negative_profit"
            elif current_win_rate < 0.15:
                print(f"❌ โมเดลแรก Win Rate ต่ำ ({current_win_rate:.1%}) - ไม่บันทึก")
                return False, "first_model_low_win_rate"
            elif current_expectancy < 0:
                print(f"❌ โมเดลแรก Expectancy ติดลบ ({current_expectancy:.2f}) - ไม่บันทึก")
                return False, "first_model_negative_expectancy"
            else:
                print("✅ โมเดลแรกมีคุณภาพดี - บันทึกโมเดลนี้")
                # บันทึกประสิทธิภาพปัจจุบันเป็นโมเดลแรก
                self._save_best_performance(current_performance, symbol, timeframe)
                return True, "first_model_good_quality"

        best_profit = best_performance.get('total_profit', 0)
        best_win_rate = best_performance.get('win_rate', 0)
        best_expectancy = best_performance.get('expectancy', 0)

        print(f"📊 โมเดลที่ดีที่สุดก่อนหน้า:")
        print(f"   💰 Total Profit: ${best_profit:,.2f}")
        print(f"   🎯 Win Rate: {best_win_rate:.1%}")
        print(f"   📈 Expectancy: {best_expectancy:.2f}")

        # เกณฑ์การตัดสินใจ
        profit_improvement = current_profit - best_profit
        win_rate_improvement = current_win_rate - best_win_rate

        # ต้องดีขึ้นอย่างน้อย 5% หรือ $500 (แต่ถ้าโมเดลเก่าขาดทุน ใช้เกณฑ์ที่ผ่อนปรน)
        if best_profit > 0:
            min_profit_improvement = max(best_profit * 0.05, 500)
        else:
            min_profit_improvement = 1000  # ถ้าโมเดลเก่าขาดทุน ต้องกำไรอย่างน้อย $1000
        
        decision_log = {
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            'timeframe': timeframe,
            'current_profit': current_profit,
            'best_profit': best_profit,
            'profit_improvement': profit_improvement,
            'min_required_improvement': min_profit_improvement,
            'current_win_rate': current_win_rate,
            'best_win_rate': best_win_rate,
            'win_rate_improvement': win_rate_improvement
        }
        
        # ตัดสินใจ
        print(f"🔍 การเปรียบเทียบ:")
        print(f"   📈 Profit Improvement: ${profit_improvement:,.2f}")
        print(f"   📊 Win Rate Improvement: {win_rate_improvement:+.1%}")
        print(f"   🎯 Min Required Improvement: ${min_profit_improvement:,.2f}")

        if current_profit < 0:
            decision = False
            reason = f"negative_profit_{current_profit:.0f}"
            print(f"❌ กำไรติดลบ (${current_profit:,.0f}) - ไม่บันทึก")

        elif current_win_rate < 0.15:  # ลดเกณฑ์จาก 0.3 เป็น 0.15 (15%)
            decision = False
            reason = f"low_win_rate_{current_win_rate:.1%}"
            print(f"❌ Win Rate ต่ำเกินไป ({current_win_rate:.1%} < 15%) - ไม่บันทึก")

        elif current_expectancy < 0:
            decision = False
            reason = f"negative_expectancy_{current_expectancy:.2f}"
            print(f"❌ Expectancy ติดลบ ({current_expectancy:.2f}) - ไม่บันทึก")

        elif profit_improvement < min_profit_improvement:
            decision = False
            reason = f"insufficient_improvement_{profit_improvement:.0f}"
            print(f"❌ ปรับปรุงไม่เพียงพอ (${profit_improvement:,.0f} < ${min_profit_improvement:,.0f}) - ไม่บันทึก")

        else:
            decision = True
            reason = f"improved_by_{profit_improvement:.0f}"
            print(f"✅ โมเดลดีขึ้น (${profit_improvement:,.0f}) - บันทึกโมเดล")
            # บันทึกประสิทธิภาพใหม่เป็นโมเดลที่ดีที่สุด
            self._save_best_performance(current_performance, symbol, timeframe)
        
        decision_log['decision'] = decision
        decision_log['reason'] = reason
        
        # บันทึก log
        self._log_decision(decision_log)
        
        return decision, reason
    
    def _load_best_performance(self, symbol, timeframe):
        """โหลดประสิทธิภาพโมเดลที่ดีที่สุด"""

        performance_file = f"best_performance_{symbol}_{timeframe}.json"

        if os.path.exists(performance_file):
            try:
                with open(performance_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ ไม่สามารถโหลดไฟล์ประสิทธิภาพ: {e}")
                return None

        return None

    def _save_best_performance(self, performance, symbol, timeframe):
        """บันทึกประสิทธิภาพโมเดลที่ดีที่สุด"""

        performance_file = f"best_performance_{symbol}_{timeframe}.json"

        try:
            with open(performance_file, 'w') as f:
                json.dump(performance, f, indent=2)
            print(f"💾 บันทึกประสิทธิภาพโมเดลที่ดีที่สุด: {performance_file}")
        except Exception as e:
            print(f"⚠️ ไม่สามารถบันทึกไฟล์ประสิทธิภาพ: {e}")
    
    def _log_decision(self, decision_log):
        """บันทึก log การตัดสินใจ"""
        
        log_entry = (
            f"[{decision_log['timestamp']}] "
            f"{decision_log['symbol']}_{decision_log['timeframe']}: "
            f"${decision_log['current_profit']:,.0f} vs ${decision_log['best_profit']:,.0f} "
            f"({decision_log['profit_improvement']:+.0f}) "
            f"→ {'SAVE' if decision_log['decision'] else 'REJECT'} "
            f"({decision_log['reason']})\n"
        )
        
        with open(self.protection_log, 'a', encoding='utf-8') as f:
            f.write(log_entry)

# การใช้งานใน LightGBM_10_4.py
# protection_system = ModelProtectionSystem(min_profit_threshold=5000)

# ก่อนบันทึกโมเดล
# should_save, reason = protection_system.should_save_model(
#     current_performance={
#         'total_profit': total_profit,
#         'win_rate': win_rate,
#         'expectancy': expectancy
#     },
#     symbol=symbol,
#     timeframe=timeframe
# )

# if should_save:
#     # บันทึกโมเดลตามปกติ
#     save_model(model, scaler, features, symbol, timeframe)
#     print(f"✅ บันทึกโมเดล: {reason}")
# else:
#     print(f"⚠️ ไม่บันทึกโมเดล: {reason}")
#     print("💡 ใช้โมเดลเดิมที่ดีกว่า")
