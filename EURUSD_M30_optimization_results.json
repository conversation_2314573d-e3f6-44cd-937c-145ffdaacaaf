{"optimization_info": {"symbol": "EURUSD", "timeframe": "M30", "source": "EURUSD_M30 specific optimization", "score": 44.2, "win_rate": 28.3, "total_profit": 1850, "total_trades": 156, "expectancy": 11.86, "max_drawdown": 1420, "optimization_date": "2025-09-28", "test_period": "Historical backtest data"}, "parameters": {"input_stop_loss_atr": 1.3, "input_take_profit": 2.8, "input_rsi_level_in": 42, "input_volume_spike": 1.4, "input_rsi_level_over": 70, "input_rsi_level_out": 30, "input_pull_back": 0.42, "input_initial_nbar_sl": 6}, "parameter_changes": {}, "performance_metrics": {"before_optimization": {"win_rate": 0.0, "total_profit": 0, "expectancy": 0.0, "max_drawdown": 0}, "after_optimization": {"win_rate": 28.3, "total_profit": 1850, "expectancy": 11.86, "max_drawdown": 1420}, "improvement": {"win_rate_improvement": 0.0, "profit_improvement": 0, "expectancy_improvement": 0.0, "drawdown_reduction": 0}}, "validation": {"cross_validation_score": 0.7366666666666667, "out_of_sample_performance": 0.8085714285714286, "parameter_stability": "Medium", "recommended_for_live_trading": false}}