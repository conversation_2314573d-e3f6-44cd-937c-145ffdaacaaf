
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting

🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   🟡 เกณฑ์ยืดหยุ่น - สมดุลระหว่างคุณภาพและการยอมรับ
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

💡 แนวคิดการบันทึกโมเดล:
   🆕 โมเดลแรก: บันทึกเสมอ (ตามเกณฑ์ของแต่ละโหมด)
   🔄 โมเดลถัดไป: เปรียบเทียบกับโมเดลก่อนหน้า

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)
current training_runs = 1

🏗️ เปิดใช้งาน create trade cycles with next close
📊 สร้าง Trade Cycles ด้วย Next Close System
   Symbol: GOLD, Timeframe: M60
   Horizons: [5, 10, 15]
   Scenarios: ['trend_following_buy', 'trend_following_sell', 'counter_trend_buy', 'counter_trend_sell']
ℹ️ training_runs=1 -> use_model_for_decision=False
ℹ️ model_confidence_threshold=0.5
ℹ️ Loaded scenario_thresholds: {'trend_following_buy': 0.5, 'trend_following_sell': 0.5, 'counter_trend_buy': 0.5, 'counter_trend_sell': 0.5}
⚠️ ไม่พบการเทรดใดๆ
Debug: create_trade_cycles_with_next_close produced empty trade_df for GOLD M60
trade_df.shape= (0, 0)
stats= {}
