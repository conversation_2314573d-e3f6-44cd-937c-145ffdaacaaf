#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ทดสอบการแก้ไข Multi-Model Architecture
เพื่อให้ GBPUSD สามารถเทรนครบ 6 โมเดลเหมือน GOLD

การแก้ไขที่ทำ:
1. ลดเกณฑ์การกรองข้อมูลจาก 500 เป็น 200 rows
2. ลดเกณฑ์ขั้นต่ำสำหรับ scenario training จาก 50 เป็น 30 samples
3. เพิ่มการตรวจสอบข้อมูล Target_Buy และ Target_Sell
4. เพิ่มการตรวจสอบ class distribution ก่อนเทรน
"""

import os
import sys
import pandas as pd

# เพิ่ม path ของโปรเจค
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_availability():
    """ทดสอบความพร้อมของข้อมูลสำหรับการเทรน Multi-Model"""
    
    print("🔍 ทดสอบความพร้อมของข้อมูลสำหรับ Multi-Model Architecture")
    print("="*70)
    
    # ทดสอบไฟล์ GBPUSD
    gbpusd_file = "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv"
    gold_file = "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    
    for file_path in [gbpusd_file, gold_file]:
        if not os.path.exists(file_path):
            print(f"❌ ไม่พบไฟล์: {file_path}")
            continue
            
        print(f"\n📊 วิเคราะห์ไฟล์: {file_path}")
        
        try:
            # อ่านไฟล์ (ใช้ comma separator สำหรับไฟล์ FIXED)
            df = pd.read_csv(file_path, sep=',', skiprows=1)  # ข้าม header row ที่ 2
            print(f"   📈 จำนวนแถว: {len(df):,}")
            print(f"   📋 จำนวนคอลัมน์: {len(df.columns)}")
            print(f"   📋 คอลัมน์: {list(df.columns)}")
            
            # ตรวจสอบคอลัมน์ที่จำเป็น (สำหรับไฟล์ raw data)
            basic_cols = ['Open', 'High', 'Low', 'Close']
            missing_basic = [col for col in basic_cols if col not in df.columns]
            
            if missing_basic:
                print(f"   ❌ ขาดคอลัมน์พื้นฐาน: {missing_basic}")
            else:
                print(f"   ✅ มีคอลัมน์พื้นฐานครบถ้วน")

                # แสดงข้อมูลพื้นฐาน
                print(f"   📊 ช่วงราคา: {df['Low'].min():.5f} - {df['High'].max():.5f}")
                print(f"   📊 ราคาปิดเฉลี่ย: {df['Close'].mean():.5f}")

                # ตรวจสอบข้อมูลที่ขาดหาย
                null_counts = df.isnull().sum()
                if null_counts.sum() > 0:
                    print(f"   ⚠️ ข้อมูลที่ขาดหาย: {dict(null_counts[null_counts > 0])}")
                else:
                    print(f"   ✅ ไม่มีข้อมูลขาดหาย")

                # ประเมินความเหมาะสมสำหรับ Multi-Model
                data_points = len(df)
                min_threshold = 200  # เกณฑ์ใหม่ที่แก้ไข

                if data_points >= min_threshold * 10:  # ต้องมีข้อมูลอย่างน้อย 2000 rows
                    print(f"   ✅ ข้อมูลเพียงพอสำหรับ Multi-Model ({data_points:,} rows)")
                else:
                    print(f"   ⚠️ ข้อมูลอาจไม่เพียงพอสำหรับ Multi-Model ({data_points:,} rows)")
                    print(f"      แนะนำ: ควรมีอย่างน้อย {min_threshold * 10:,} rows")
            
            # ตรวจสอบ Target columns (ถ้ามี)
            target_cols = ['Target', 'Target_Multiclass', 'Target_Buy', 'Target_Sell']
            found_targets = [col for col in target_cols if col in df.columns]
            
            if found_targets:
                print(f"   📋 Target columns ที่พบ: {found_targets}")
                
                for col in found_targets:
                    if col in ['Target_Buy', 'Target_Sell']:
                        # สำหรับ Target_Buy/Sell ตรวจสอบข้อมูลที่ไม่ใช่ -1
                        valid_data = (df[col] != -1).sum()
                        print(f"      {col}: {valid_data:,}/{len(df):,} valid samples ({valid_data/len(df)*100:.1f}%)")
                    else:
                        # สำหรับ Target อื่นๆ แสดง distribution
                        value_counts = df[col].value_counts()
                        print(f"      {col} distribution: {dict(value_counts)}")
            else:
                print(f"   ℹ️ ไม่พบ Target columns (ปกติสำหรับไฟล์ raw data)")
                
        except Exception as e:
            print(f"   ❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {e}")

def show_improvements():
    """แสดงการปรับปรุงที่ทำ"""
    
    print("\n🛠️ การปรับปรุงที่ทำเพื่อแก้ไขปัญหา Multi-Model Architecture")
    print("="*70)
    
    improvements = [
        {
            "title": "1. ลดเกณฑ์การกรองข้อมูล",
            "before": "ข้อมูลต้อง >= 500 rows หลังกรอง scenario",
            "after": "ข้อมูลต้อง >= 200 rows หลังกรอง scenario",
            "impact": "ทำให้ข้อมูลผ่านการกรองได้ง่ายขึ้น"
        },
        {
            "title": "2. ลดเกณฑ์ขั้นต่ำสำหรับ scenario training",
            "before": "ต้องมีอย่างน้อย 50 samples (MIN_TRAINING_SAMPLES // 4)",
            "after": "ต้องมีอย่างน้อย 30 samples (MIN_TRAINING_SAMPLES // 6)",
            "impact": "ทำให้สามารถเทรนได้แม้ข้อมูลน้อย"
        },
        {
            "title": "3. เพิ่มการตรวจสอบ Target_Buy/Target_Sell",
            "before": "ไม่มีการตรวจสอบข้อมูลก่อนเทรน",
            "after": "ตรวจสอบว่ามีข้อมูล >= 100 samples และ >= 10 samples ต่อ class",
            "impact": "ป้องกันการเทรนด้วยข้อมูลไม่เพียงพอ"
        },
        {
            "title": "4. เพิ่มการ debug และ logging",
            "before": "ข้อมูล debug จำกัด",
            "after": "แสดงสถิติข้อมูลและ class distribution อย่างละเอียด",
            "impact": "ช่วยในการ troubleshoot ปัญหา"
        }
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"\n{improvement['title']}:")
        print(f"   ก่อน: {improvement['before']}")
        print(f"   หลัง: {improvement['after']}")
        print(f"   ผลกระทบ: {improvement['impact']}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🧪 สคริปต์ทดสอบการแก้ไข Multi-Model Architecture")
    print("="*70)
    print("วัตถุประสงค์: ทำให้ GBPUSD สามารถเทรนครบ 6 โมเดลเหมือน GOLD")
    print()
    
    # ทดสอบความพร้อมของข้อมูล
    test_data_availability()
    
    # แสดงการปรับปรุง
    show_improvements()
    
    print("\n📋 ขั้นตอนต่อไป:")
    print("1. รันการเทรนด้วยไฟล์ GBPUSD_H1_FIXED.csv")
    print("2. ตรวจสอบว่าได้โมเดลครบ 6 โมเดลหรือไม่")
    print("3. เปรียบเทียบผลลัพธ์กับ GOLD")
    print()
    print("🚀 คำสั่งรัน: python LightGBM_10_4.py")

if __name__ == "__main__":
    main()
