#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขปัญหา DateTime Column Missing
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def test_datetime_creation():
    """ทดสอบการสร้าง DateTime column"""
    print("🔍 ทดสอบการสร้าง DateTime column")
    
    # สร้าง DataFrame ทดสอบแบบต่างๆ
    test_cases = [
        {
            'name': 'มี Date และ Time columns',
            'df': pd.DataFrame({
                'Date': ['2023.01.01', '2023.01.01', '2023.01.01'],
                'Time': ['10:00:00', '11:00:00', '12:00:00'],
                'Close': [1.1000, 1.1010, 1.1020]
            })
        },
        {
            'name': 'ไม่มี Date/Time columns',
            'df': pd.DataFrame({
                'Close': [1.1000, 1.1010, 1.1020],
                'Volume': [100, 200, 150]
            })
        },
        {
            'name': 'มี DateTime column อยู่แล้ว',
            'df': pd.DataFrame({
                'DateTime': pd.date_range('2023-01-01 10:00:00', periods=3, freq='H'),
                'Close': [1.1000, 1.1010, 1.1020]
            })
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {test_case['name']} ---")
        combined_df = test_case['df'].copy()
        
        try:
            # ใช้ logic เดียวกับที่แก้ไขใน LightGBM_10_4.py
            print("🔍 ตรวจสอบคอลัมน์ DateTime ใน combined_df")
            print(f"   Columns ใน combined_df: {list(combined_df.columns)}")
            
            if 'DateTime' not in combined_df.columns:
                print("⚠️ ไม่พบคอลัมน์ 'DateTime' ใน combined_df - กำลังสร้างใหม่")
                
                # วิธีที่ 1: ลองสร้างจาก Date + Time
                if 'Date' in combined_df.columns and 'Time' in combined_df.columns:
                    try:
                        combined_df['DateTime'] = pd.to_datetime(combined_df['Date'] + ' ' + combined_df['Time'], errors='coerce')
                        print("✅ สร้าง DateTime จาก Date + Time สำเร็จ")
                    except Exception as e:
                        print(f"⚠️ ไม่สามารถสร้าง DateTime จาก Date + Time: {e}")
                        combined_df['DateTime'] = None
                
                # วิธีที่ 2: ถ้ายังไม่ได้ ใช้ index หรือสร้าง sequence
                if 'DateTime' not in combined_df.columns or combined_df['DateTime'].isna().all():
                    print("🔄 ใช้ fallback method: สร้าง DateTime sequence")
                    try:
                        # สร้าง datetime sequence (1 ชั่วโมงต่อแถว)
                        end_time = datetime.now()
                        start_time = end_time - timedelta(hours=len(combined_df)-1)
                        combined_df['DateTime'] = pd.date_range(start=start_time, periods=len(combined_df), freq='H')
                        print(f"✅ สร้าง DateTime sequence จาก {start_time} ถึง {end_time}")
                    except Exception as e:
                        print(f"❌ ไม่สามารถสร้าง DateTime sequence: {e}")
                        # สุดท้าย: ใช้ index เป็น datetime
                        combined_df['DateTime'] = pd.to_datetime(combined_df.index, errors='coerce')
                        print("✅ ใช้ index เป็น DateTime")
            else:
                print("✅ พบคอลัมน์ DateTime ใน combined_df แล้ว")
            
            # แปลง 'DateTime' ให้เป็น datetime และเรียงลำดับ
            try:
                combined_df['DateTime'] = pd.to_datetime(combined_df['DateTime'], errors='coerce')
                combined_df.sort_values('DateTime', inplace=True)
                print(f"✅ แปลงและเรียงลำดับ DateTime สำเร็จ (ช่วงเวลา: {combined_df['DateTime'].min()} ถึง {combined_df['DateTime'].max()})")
                
                # แสดงผลลัพธ์
                print("📊 ผลลัพธ์:")
                print(combined_df[['DateTime'] + [col for col in combined_df.columns if col != 'DateTime']].head())
                
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดในการจัดการ DateTime: {e}")
                
        except Exception as e:
            print(f"❌ Test case ล้มเหลว: {e}")
            import traceback
            traceback.print_exc()

def test_financial_analysis_charts():
    """ทดสอบการสร้างกราฟ Financial Analysis"""
    print("\n🎨 ทดสอบการสร้างกราฟ Financial Analysis")
    
    # สร้างข้อมูลทดสอบ
    financial_data = {
        'GBPUSD_M30': {
            'total_profit': -5000,
            'win_rate': 0.25,
            'total_trades': 1000,
            'max_drawdown': -8000,
            'expectancy': -5.0,
            'sharpe_ratio': -0.5
        },
        'GBPUSD_M60': {
            'total_profit': -3000,
            'win_rate': 0.30,
            'total_trades': 800,
            'max_drawdown': -5000,
            'expectancy': -3.75,
            'sharpe_ratio': -0.3
        },
        'EURUSD_M30': {
            'total_profit': 2000,
            'win_rate': 0.55,
            'total_trades': 1200,
            'max_drawdown': -3000,
            'expectancy': 1.67,
            'sharpe_ratio': 0.4
        },
        'EURUSD_M60': {
            'total_profit': 1500,
            'win_rate': 0.52,
            'total_trades': 900,
            'max_drawdown': -2500,
            'expectancy': 1.67,
            'sharpe_ratio': 0.3
        }
    }
    
    try:
        # สร้างโฟลเดอร์ทดสอบ
        test_output_dir = "Test_Financial_Charts"
        os.makedirs(test_output_dir, exist_ok=True)
        
        # เรียกใช้ฟังก์ชันจาก LightGBM_10_4.py
        import sys
        sys.path.append('.')
        
        # Import ฟังก์ชันที่เพิ่มใหม่
        try:
            from LightGBM_10_4 import create_enhanced_financial_charts
            create_enhanced_financial_charts(financial_data, test_output_dir)
            print("✅ ทดสอบการสร้างกราฟสำเร็จ")
        except ImportError as e:
            print(f"⚠️ ไม่สามารถ import ฟังก์ชันได้: {e}")
            print("🔄 สร้างฟังก์ชันทดสอบแทน")
            create_simple_test_charts(financial_data, test_output_dir)
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบกราฟ: {e}")
        import traceback
        traceback.print_exc()

def create_simple_test_charts(financial_data, output_dir):
    """สร้างกราฟทดสอบแบบง่าย"""
    try:
        import matplotlib.pyplot as plt
        
        # แยกข้อมูลตาม timeframe
        timeframes = {}
        for key, data in financial_data.items():
            if '_' in key:
                symbol, timeframe = key.split('_')
                if timeframe not in timeframes:
                    timeframes[timeframe] = {}
                timeframes[timeframe][symbol] = data
        
        # สร้างกราฟแยกตาม timeframe
        for timeframe, tf_data in timeframes.items():
            fig, axes = plt.subplots(2, 2, figsize=(12, 8))
            fig.suptitle(f'Trading Performance - {timeframe}', fontsize=14)
            
            symbols = list(tf_data.keys())
            profits = [tf_data[s]['total_profit'] for s in symbols]
            win_rates = [tf_data[s]['win_rate'] * 100 for s in symbols]
            
            # กราฟ Profit
            axes[0, 0].bar(symbols, profits, color=['green' if p > 0 else 'red' for p in profits])
            axes[0, 0].set_title('Total Profit')
            axes[0, 0].set_ylabel('Profit ($)')
            
            # กราฟ Win Rate
            axes[0, 1].bar(symbols, win_rates, color='blue', alpha=0.7)
            axes[0, 1].set_title('Win Rate')
            axes[0, 1].set_ylabel('Win Rate (%)')
            axes[0, 1].axhline(y=50, color='red', linestyle='--', alpha=0.5)
            
            plt.tight_layout()
            
            chart_path = os.path.join(output_dir, f'test_chart_{timeframe}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()
            print(f"✅ บันทึกกราฟทดสอบ {timeframe}: {chart_path}")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้างกราฟทดสอบ: {e}")

if __name__ == "__main__":
    print("🚀 เริ่มทดสอบการแก้ไขปัญหา")
    print("=" * 60)
    
    # ทดสอบการสร้าง DateTime
    test_datetime_creation()
    
    # ทดสอบการสร้างกราฟ
    test_financial_analysis_charts()
    
    print("\n" + "=" * 60)
    print("🎉 ทดสอบเสร็จสิ้น")
