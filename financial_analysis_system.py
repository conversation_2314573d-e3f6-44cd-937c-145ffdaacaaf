#!/usr/bin/env python3
"""
Financial Analysis System for Trading Performance
ระบบวิเคราะห์ทางการเงินสำหรับประสิทธิภาพการเทรด

Features:
- คำนวณ pips value และ margin สำหรับแต่ละสัญลักษณ์
- วิเคราะห์ DDmax, Profit, Risk Management
- สร้างกราฟแสดงผลการเทรด
- คำนวณทุนที่ต้องใช้และความเสี่ยงที่เหมาะสม
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, List, Tuple, Optional

# Import enhanced logging system
try:
    from enhanced_logging_system import EnhancedLogger, LoggedOperation, log_function
    LOGGING_AVAILABLE = True
except ImportError:
    LOGGING_AVAILABLE = False
    print("⚠️ Enhanced logging system not available - using basic logging")
import warnings
warnings.filterwarnings('ignore')

class FinancialAnalysisSystem:
    """ระบบวิเคราะห์ทางการเงินสำหรับการเทรด"""

    def __init__(self, base_currency='USD', leverage=500):
        self.base_currency = base_currency
        self.leverage = leverage

        # Initialize enhanced logging
        if LOGGING_AVAILABLE:
            self.logger = EnhancedLogger("FinancialAnalysis")
            self.logger.info("🏗️ เปิดใช้งาน Financial Analysis System", {
                'base_currency': base_currency,
                'leverage': leverage
            })
        else:
            self.logger = None
        
        # กำหนดข้อมูลสัญลักษณ์
        self.symbol_specs = {
            'GOLD': {'point': 0.01, 'contract_size': 100, 'type': 'metal'},
            'EURUSD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_quote'},
            'GBPUSD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_quote'},
            'AUDUSD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_quote'},
            'NZDUSD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_quote'},
            'USDCAD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_base'},
            'USDJPY': {'point': 0.001, 'contract_size': 100000, 'type': 'major_usd_base'}
        }
        
        # เก็บข้อมูลการเทรดทั้งหมด
        self.all_trades = []
        self.symbol_data = {}
        
        # สร้างโฟลเดอร์สำหรับบันทึกผล
        self.results_folder = "Financial_Analysis_Results"
        os.makedirs(self.results_folder, exist_ok=True)
        
    def calculate_pips_value_and_margin(self, symbol: str, current_price: float, lot_size: float = 0.01) -> Dict:
        """
        คำนวณ pips value และ margin สำหรับสัญลักษณ์
        
        Args:
            symbol: สัญลักษณ์ (เช่น GOLD, EURUSD)
            current_price: ราคาปัจจุบัน
            lot_size: ขนาดล็อต (default: 0.01)
            
        Returns:
            Dict: {'pips_value_per_point', 'pips_value_per_pip', 'margin_required'}
        """
        if symbol not in self.symbol_specs:
            raise ValueError(f"Symbol {symbol} not supported")
            
        spec = self.symbol_specs[symbol]
        point = spec['point']
        contract_size = spec['contract_size']
        symbol_type = spec['type']
        
        # คำนวณ pips value
        if symbol_type == 'metal':  # GOLD
            pips_value_per_point = (point / current_price) * current_price * contract_size * lot_size
            margin_required = (lot_size * contract_size * current_price) / self.leverage
            
        elif symbol_type == 'major_usd_quote':  # EURUSD, GBPUSD, etc.
            pips_value_per_point = (point / current_price) * current_price * contract_size * lot_size
            margin_required = (lot_size * contract_size * current_price) / self.leverage
            
        elif symbol_type == 'major_usd_base':  # USDJPY, USDCAD
            pips_value_per_point = (point / current_price) * contract_size * lot_size
            margin_required = (lot_size * contract_size) / self.leverage
            
        else:
            raise ValueError(f"Unknown symbol type: {symbol_type}")
        
        # คำนวณ pips value per pip (10 points = 1 pip สำหรับส่วนใหญ่)
        pips_per_point_multiplier = 10 if point == 0.00001 or point == 0.001 else 1
        pips_value_per_pip = pips_value_per_point * pips_per_point_multiplier
        
        return {
            'pips_value_per_point': pips_value_per_point,
            'pips_value_per_pip': pips_value_per_pip,
            'margin_required': margin_required,
            'point': point,
            'contract_size': contract_size
        }
    
    def process_trade_cycle(self, symbol: str, timeframe: str, trade_data: pd.DataFrame, 
                           current_prices: Dict[str, float]) -> Dict:
        """
        ประมวลผลข้อมูลการเทรดสำหรับสัญลักษณ์หนึ่ง
        
        Args:
            symbol: สัญลักษณ์
            timeframe: ไทม์เฟรม (M30, M60)
            trade_data: DataFrame ที่มีข้อมูลการเทรด
            current_prices: Dict ของราคาปัจจุบันแต่ละสัญลักษณ์
            
        Returns:
            Dict: ข้อมูลการวิเคราะห์ทางการเงิน
        """

        if self.logger:
            self.logger.log_function_start("process_trade_cycle",
                symbol=symbol, timeframe=timeframe, trades_count=len(trade_data))

        try:
            if symbol not in current_prices:
                error_msg = f"ไม่มีราคาปัจจุบันสำหรับ {symbol}"
                if self.logger:
                    self.logger.warning(error_msg, {'symbol': symbol, 'available_symbols': list(current_prices.keys())})
                else:
                    print(f"⚠️ {error_msg}")
                return None

            # คำนวณ pips value และ margin
            financial_data = self.calculate_pips_value_and_margin(symbol, current_prices[symbol], 1.0)

            # ประมวลผลข้อมูลการเทรด
            trades_processed = []

            for idx, trade in trade_data.iterrows():
                # คำนวณกำไร/ขาดทุนในสกุลเงินหลัก
                pips_profit = trade.get('pips_profit', 0)  # กำไรในหน่วย pips
                usd_profit = pips_profit * financial_data['pips_value_per_pip']

                trade_info = {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'open_time': trade.get('open_time'),
                    'close_time': trade.get('close_time'),
                    'direction': trade.get('direction', 'BUY'),
                    'open_price': trade.get('open_price', 0),
                    'close_price': trade.get('close_price', 0),
                    'pips_profit': pips_profit,
                    'usd_profit': usd_profit,
                    'margin_required': financial_data['margin_required'],
                    'pips_value_per_pip': financial_data['pips_value_per_pip'],
                    'lot_size': 1.0  # ขนาดล็อตคงที่
                }

                trades_processed.append(trade_info)

            # คำนวณสถิติ
            if trades_processed:
                profits = [t['usd_profit'] for t in trades_processed]
                cumulative_profit = np.cumsum(profits)

                # คำนวณ Drawdown
                running_max = np.maximum.accumulate(cumulative_profit)
                drawdown = cumulative_profit - running_max
                max_drawdown = abs(min(drawdown)) if len(drawdown) > 0 else 0

                analysis_result = {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'total_trades': len(trades_processed),
                    'total_profit_usd': sum(profits),
                    'max_drawdown_usd': max_drawdown,
                    'margin_per_trade': financial_data['margin_required'],
                    'pips_value_per_pip': financial_data['pips_value_per_pip'],
                    'trades': trades_processed,
                    'financial_specs': financial_data
                }

                # บันทึกข้อมูลสำหรับสัญลักษณ์นี้
                self.symbol_data[f"{symbol}_{timeframe}"] = analysis_result

                if self.logger:
                    self.logger.log_financial_analysis(symbol, timeframe,
                        total_trades=len(trades_processed),
                        total_profit_usd=sum(profits),
                        max_drawdown_usd=max_drawdown,
                        margin_per_trade=financial_data['margin_required']
                    )

                return analysis_result

        except Exception as e:
            if self.logger:
                self.logger.error(f"ข้อผิดพลาดในการประมวลผล {symbol}_{timeframe}", e, {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'trades_count': len(trade_data)
                })
            else:
                print(f"❌ ข้อผิดพลาดในการประมวลผล {symbol}_{timeframe}: {e}")
            return None

        return None
    
    def save_individual_analysis(self, symbol: str, timeframe: str, analysis_data: Dict):
        """บันทึกผลการวิเคราะห์แต่ละสัญลักษณ์"""
        filename = f"{self.results_folder}/{symbol}_{timeframe}_financial_analysis.json"
        
        # แปลง datetime objects เป็น string สำหรับ JSON
        data_to_save = analysis_data.copy()
        for trade in data_to_save.get('trades', []):
            if 'open_time' in trade and hasattr(trade['open_time'], 'isoformat'):
                trade['open_time'] = trade['open_time'].isoformat()
            if 'close_time' in trade and hasattr(trade['close_time'], 'isoformat'):
                trade['close_time'] = trade['close_time'].isoformat()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"💾 บันทึกการวิเคราะห์ {symbol} {timeframe} ที่: {filename}")
    
    def combine_all_analyses(self) -> Dict:
        """รวมการวิเคราะห์ทั้งหมดเข้าด้วยกัน"""
        all_trades = []
        total_profit = 0
        total_margin_required = 0
        
        # รวมข้อมูลจากทุกสัญลักษณ์
        for key, data in self.symbol_data.items():
            all_trades.extend(data['trades'])
            total_profit += data['total_profit_usd']
            total_margin_required += data['margin_per_trade']
        
        # เรียงตามเวลาเปิดการเทรด
        all_trades.sort(key=lambda x: x['open_time'] if x['open_time'] else datetime.min)
        
        # คำนวณสถิติรวม
        if all_trades:
            profits = [t['usd_profit'] for t in all_trades]
            cumulative_profit = np.cumsum(profits)
            
            # คำนวณ Drawdown รวม
            running_max = np.maximum.accumulate(cumulative_profit)
            drawdown = cumulative_profit - running_max
            max_drawdown = abs(min(drawdown)) if len(drawdown) > 0 else 0
            
            combined_analysis = {
                'total_trades': len(all_trades),
                'total_profit_usd': total_profit,
                'max_drawdown_usd': max_drawdown,
                'total_margin_required': total_margin_required,
                'trades': all_trades,
                'symbols_analyzed': list(self.symbol_data.keys()),
                'analysis_date': datetime.now().isoformat()
            }
            
            return combined_analysis
        
        return None

    def create_performance_plots(self, combined_analysis: Dict):
        """สร้างกราฟแสดงผลการเทรด"""
        if not combined_analysis or not combined_analysis['trades']:
            print("⚠️ ไม่มีข้อมูลการเทรดสำหรับสร้างกราฟ")
            return

        trades = combined_analysis['trades']

        # เตรียมข้อมูลสำหรับกราฟ
        dates = []
        cumulative_profits = []
        drawdowns = []

        cumulative = 0
        running_max = 0

        for trade in trades:
            if trade['close_time']:
                try:
                    if isinstance(trade['close_time'], str):
                        close_time = datetime.fromisoformat(trade['close_time'].replace('Z', '+00:00'))
                    else:
                        close_time = trade['close_time']

                    dates.append(close_time)
                    cumulative += trade['usd_profit']
                    cumulative_profits.append(cumulative)

                    # คำนวณ drawdown
                    running_max = max(running_max, cumulative)
                    drawdown = cumulative - running_max
                    drawdowns.append(drawdown)

                except Exception as e:
                    print(f"⚠️ Error processing date: {e}")
                    continue

        if not dates:
            print("⚠️ ไม่มีข้อมูลวันที่ที่ถูกต้องสำหรับสร้างกราฟ")
            return

        # สร้างกราฟ
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # กราฟกำไรสะสม
        ax1.plot(dates, cumulative_profits, 'b-', linewidth=2, label='Cumulative Profit')
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax1.set_title('Cumulative Profit Over Time (USD)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Profit (USD)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Format x-axis
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

        # กราฟ Drawdown
        ax2.fill_between(dates, drawdowns, 0, color='red', alpha=0.3, label='Drawdown')
        ax2.plot(dates, drawdowns, 'r-', linewidth=2)
        ax2.set_title('Drawdown Over Time (USD)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Date', fontsize=12)
        ax2.set_ylabel('Drawdown (USD)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # Format x-axis
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # บันทึกกราฟ
        plot_filename = f"{self.results_folder}/trading_performance_analysis.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 บันทึกกราฟที่: {plot_filename}")

    def calculate_risk_management(self, combined_analysis: Dict, account_balance: float = 1000) -> Dict:
        """
        คำนวณการจัดการความเสี่ยงและขนาดการเทรดที่เหมาะสม

        Args:
            combined_analysis: ผลการวิเคราะห์รวม
            account_balance: ยอดเงินในบัญชี (USD)

        Returns:
            Dict: ข้อมูลการจัดการความเสี่ยง
        """
        if not combined_analysis:
            return None

        max_dd_usd = combined_analysis['max_drawdown_usd']
        total_margin = combined_analysis['total_margin_required']

        # กำหนดระดับความเสี่ยง
        risk_levels = {
            'daily_risk_2pct': account_balance * 0.02,      # 2% ต่อวัน
            'weekly_risk_5pct': account_balance * 0.05,     # 5% ต่อสัปดาห์
            'total_risk_10pct': account_balance * 0.10      # 10% ของทั้งหมด
        }

        # คำนวณขนาดการเทรดที่เหมาะสม (lot size multiplier)
        safe_multipliers = {}

        if max_dd_usd > 0:
            # คำนวณตาม drawdown
            safe_multipliers['based_on_daily_risk'] = risk_levels['daily_risk_2pct'] / max_dd_usd
            safe_multipliers['based_on_weekly_risk'] = risk_levels['weekly_risk_5pct'] / max_dd_usd
            safe_multipliers['based_on_total_risk'] = risk_levels['total_risk_10pct'] / max_dd_usd

        # คำนวณตาม margin requirement
        if total_margin > 0:
            safe_multipliers['based_on_margin'] = account_balance / (total_margin * 2)  # ใช้ 50% ของเงินสำหรับ margin

        # เลือกค่าที่ปลอดภัยที่สุด
        if safe_multipliers:
            recommended_multiplier = min(safe_multipliers.values())
            recommended_lot_size = recommended_multiplier * 1.0  # base lot size = 1.0
        else:
            recommended_multiplier = 0.01
            recommended_lot_size = 0.01

        risk_analysis = {
            'account_balance': account_balance,
            'max_drawdown_at_1lot': max_dd_usd,
            'total_margin_required': total_margin,
            'risk_levels': risk_levels,
            'safe_multipliers': safe_multipliers,
            'recommended_multiplier': recommended_multiplier,
            'recommended_lot_size': recommended_lot_size,
            'max_risk_percentage': (max_dd_usd * recommended_multiplier / account_balance) * 100
        }

        return risk_analysis

    def create_risk_table(self, risk_analysis: Dict) -> pd.DataFrame:
        """สร้างตารางแสดงความเสี่ยงในระดับต่างๆ"""
        if not risk_analysis:
            return None

        # สร้างตารางสำหรับระดับความเสี่ยงต่างๆ
        risk_percentages = [0.1, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0]  # เปอร์เซ็นต์ความเสี่ยง
        account_balance = risk_analysis['account_balance']
        max_dd_at_1lot = risk_analysis['max_drawdown_at_1lot']

        table_data = []

        for risk_pct in risk_percentages:
            risk_amount = account_balance * (risk_pct / 100)

            if max_dd_at_1lot > 0:
                max_lot_size = risk_amount / max_dd_at_1lot
                max_risk_actual = (max_dd_at_1lot * max_lot_size / account_balance) * 100
            else:
                max_lot_size = 0
                max_risk_actual = 0

            table_data.append({
                'Risk_Percentage': f"{risk_pct}%",
                'Risk_Amount_USD': f"${risk_amount:.2f}",
                'Max_Lot_Size': f"{max_lot_size:.4f}",
                'Actual_Risk_Pct': f"{max_risk_actual:.2f}%",
                'Status': 'Safe' if risk_pct <= 2.0 else 'Moderate' if risk_pct <= 5.0 else 'High Risk'
            })

        df = pd.DataFrame(table_data)
        return df

    def save_complete_analysis(self, combined_analysis: Dict, risk_analysis: Dict, risk_table: pd.DataFrame):
        """บันทึกการวิเคราะห์ทั้งหมด"""

        # บันทึกข้อมูลรวม JSON
        summary_filename = f"{self.results_folder}/complete_financial_analysis.json"

        summary_data = {
            'analysis_summary': {
                'total_trades': combined_analysis['total_trades'],
                'total_profit_usd': combined_analysis['total_profit_usd'],
                'max_drawdown_usd': combined_analysis['max_drawdown_usd'],
                'total_margin_required': combined_analysis['total_margin_required'],
                'symbols_analyzed': combined_analysis['symbols_analyzed']
            },
            'risk_management': risk_analysis,
            'analysis_date': combined_analysis['analysis_date']
        }

        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False, default=str)

        # บันทึกตารางความเสี่ยง CSV
        risk_table_filename = f"{self.results_folder}/risk_management_table.csv"
        risk_table.to_csv(risk_table_filename, index=False)

        # บันทึกรายงานข้อความ
        report_filename = f"{self.results_folder}/financial_analysis_report.txt"

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("FINANCIAL ANALYSIS REPORT\n")
            f.write("=" * 80 + "\n\n")

            f.write(f"Analysis Date: {combined_analysis['analysis_date']}\n")
            f.write(f"Account Balance: ${risk_analysis['account_balance']:,.2f}\n")
            f.write(f"Leverage: 1:{self.leverage}\n\n")

            f.write("TRADING PERFORMANCE SUMMARY:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Total Trades: {combined_analysis['total_trades']}\n")
            f.write(f"Total Profit (1.0 lot): ${combined_analysis['total_profit_usd']:,.2f}\n")
            f.write(f"Max Drawdown (1.0 lot): ${combined_analysis['max_drawdown_usd']:,.2f}\n")
            f.write(f"Total Margin Required: ${combined_analysis['total_margin_required']:,.2f}\n\n")

            f.write("RISK MANAGEMENT RECOMMENDATIONS:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Recommended Lot Size: {risk_analysis['recommended_lot_size']:.4f}\n")
            f.write(f"Max Risk Percentage: {risk_analysis['max_risk_percentage']:.2f}%\n\n")

            f.write("SYMBOLS ANALYZED:\n")
            f.write("-" * 40 + "\n")
            for symbol in combined_analysis['symbols_analyzed']:
                f.write(f"- {symbol}\n")

            f.write("\n" + "=" * 80 + "\n")

        print(f"💾 บันทึกการวิเคราะห์สมบูรณ์:")
        print(f"   📄 Summary: {summary_filename}")
        print(f"   📊 Risk Table: {risk_table_filename}")
        print(f"   📝 Report: {report_filename}")

    def create_enhanced_financial_charts(self):
        """สร้างกราฟ Financial Analysis แยกตาม timeframe และ symbol"""
        try:
            print("\n🎨 สร้างกราฟ Financial Analysis แบบแยกรายละเอียด")

            if not self.symbol_data:
                print("⚠️ ไม่มีข้อมูลสำหรับสร้างกราฟ")
                return

            # แปลงข้อมูลให้เข้ากับรูปแบบที่ต้องการ
            financial_data = {}
            for key, data in self.symbol_data.items():
                # คำนวณสถิติเพิ่มเติม
                win_rate = 0
                expectancy = 0
                sharpe_ratio = 0
                profit_factor = 0
                recovery_factor = 0

                if data['trades']:
                    profits = [t['usd_profit'] for t in data['trades']]
                    wins = [p for p in profits if p > 0]
                    losses = [p for p in profits if p < 0]

                    win_rate = len(wins) / len(profits) if profits else 0
                    expectancy = sum(profits) / len(profits) if profits else 0

                    # Profit Factor
                    total_wins = sum(wins) if wins else 0
                    total_losses = abs(sum(losses)) if losses else 0
                    profit_factor = total_wins / total_losses if total_losses > 0 else 0

                    # Recovery Factor
                    recovery_factor = data['total_profit_usd'] / data['max_drawdown_usd'] if data['max_drawdown_usd'] > 0 else 0

                    # Sharpe Ratio (simplified)
                    if len(profits) > 1:
                        import numpy as np
                        sharpe_ratio = np.mean(profits) / np.std(profits) if np.std(profits) > 0 else 0

                financial_data[key] = {
                    'total_profit': data['total_profit_usd'],
                    'win_rate': win_rate,
                    'total_trades': data['total_trades'],
                    'max_drawdown': data['max_drawdown_usd'],
                    'expectancy': expectancy,
                    'sharpe_ratio': sharpe_ratio,
                    'profit_factor': profit_factor,
                    'recovery_factor': recovery_factor
                }

            # เรียกใช้ฟังก์ชันสร้างกราฟ
            self._create_timeframe_charts(financial_data)

        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการสร้างกราฟแยกรายละเอียด: {e}")
            import traceback
            traceback.print_exc()

    def _create_timeframe_charts(self, financial_data):
        """สร้างกราฟแยกตาม timeframe และ symbol"""
        try:
            import matplotlib.pyplot as plt
            import pandas as pd

            # แยกข้อมูลตาม timeframe
            timeframes = {}
            symbols = set()

            # วิเคราะห์ข้อมูลจาก financial_data
            for key, data in financial_data.items():
                if isinstance(key, str) and '_' in key:
                    parts = key.split('_')
                    if len(parts) >= 2:
                        symbol = parts[0]
                        timeframe = parts[1]
                        symbols.add(symbol)

                        if timeframe not in timeframes:
                            timeframes[timeframe] = {}
                        timeframes[timeframe][symbol] = data

            print(f"📊 พบ timeframes: {list(timeframes.keys())}")
            print(f"📊 พบ symbols: {list(symbols)}")

            # สร้างกราฟแยกตาม timeframe
            for timeframe, tf_data in timeframes.items():
                if not tf_data:
                    continue

                self._create_timeframe_chart(timeframe, tf_data)
                self._create_timeframe_csv(timeframe, tf_data)

            # สร้างกราฟเปรียบเทียบ M30 vs M60
            if 'M30' in timeframes and 'M60' in timeframes:
                self._create_comparison_chart(timeframes)

            print("🎉 สร้างกราฟ Financial Analysis แบบแยกรายละเอียดเสร็จสิ้น")

        except ImportError:
            print("⚠️ ไม่พบ matplotlib - ข้ามการสร้างกราฟ")
        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการสร้างกราฟ: {e}")
            import traceback
            traceback.print_exc()

    def _create_timeframe_chart(self, timeframe, tf_data):
        """สร้างกราฟสำหรับ timeframe เฉพาะ"""
        try:
            import matplotlib.pyplot as plt
            import os

            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Trading Performance Analysis - {timeframe}', fontsize=16, fontweight='bold')

            # เตรียมข้อมูลสำหรับกราฟ
            symbol_names = list(tf_data.keys())
            profits = []
            win_rates = []
            total_trades = []
            max_drawdowns = []

            for symbol in symbol_names:
                data = tf_data[symbol]
                profits.append(data.get('total_profit', 0))
                win_rates.append(data.get('win_rate', 0) * 100)  # แปลงเป็น %
                total_trades.append(data.get('total_trades', 0))
                max_drawdowns.append(abs(data.get('max_drawdown', 0)))

            # กราฟ 1: Total Profit by Symbol
            axes[0, 0].bar(symbol_names, profits, color=['green' if p > 0 else 'red' for p in profits])
            axes[0, 0].set_title('Total Profit by Symbol')
            axes[0, 0].set_ylabel('Profit ($)')
            axes[0, 0].tick_params(axis='x', rotation=45)
            axes[0, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)

            # กราฟ 2: Win Rate by Symbol
            axes[0, 1].bar(symbol_names, win_rates, color='blue', alpha=0.7)
            axes[0, 1].set_title('Win Rate by Symbol')
            axes[0, 1].set_ylabel('Win Rate (%)')
            axes[0, 1].tick_params(axis='x', rotation=45)
            axes[0, 1].axhline(y=50, color='red', linestyle='--', alpha=0.5, label='50% Line')
            axes[0, 1].legend()

            # กราฟ 3: Total Trades by Symbol
            axes[1, 0].bar(symbol_names, total_trades, color='orange', alpha=0.7)
            axes[1, 0].set_title('Total Trades by Symbol')
            axes[1, 0].set_ylabel('Number of Trades')
            axes[1, 0].tick_params(axis='x', rotation=45)

            # กราฟ 4: Max Drawdown by Symbol
            axes[1, 1].bar(symbol_names, max_drawdowns, color='red', alpha=0.7)
            axes[1, 1].set_title('Max Drawdown by Symbol')
            axes[1, 1].set_ylabel('Max Drawdown ($)')
            axes[1, 1].tick_params(axis='x', rotation=45)

            plt.tight_layout()

            # บันทึกกราฟ
            chart_path = os.path.join(self.results_folder, f'trading_performance_{timeframe}.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"✅ บันทึกกราฟ {timeframe} ที่: {chart_path}")

        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการสร้างกราฟ {timeframe}: {e}")

    def _create_timeframe_csv(self, timeframe, tf_data):
        """สร้างไฟล์ CSV สำหรับ timeframe เฉพาะ"""
        try:
            import pandas as pd
            import os

            csv_data = []
            for symbol, data in tf_data.items():
                csv_data.append({
                    'Symbol': symbol,
                    'Timeframe': timeframe,
                    'Total_Profit': data.get('total_profit', 0),
                    'Win_Rate': data.get('win_rate', 0),
                    'Total_Trades': data.get('total_trades', 0),
                    'Max_Drawdown': data.get('max_drawdown', 0),
                    'Expectancy': data.get('expectancy', 0),
                    'Sharpe_Ratio': data.get('sharpe_ratio', 0),
                    'Profit_Factor': data.get('profit_factor', 0),
                    'Recovery_Factor': data.get('recovery_factor', 0)
                })

            csv_df = pd.DataFrame(csv_data)
            csv_path = os.path.join(self.results_folder, f'trading_summary_{timeframe}.csv')
            csv_df.to_csv(csv_path, index=False)
            print(f"✅ บันทึกไฟล์ CSV {timeframe} ที่: {csv_path}")

        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการสร้างไฟล์ CSV {timeframe}: {e}")

    def _create_comparison_chart(self, timeframes):
        """สร้างกราฟเปรียบเทียบ M30 vs M60"""
        try:
            import matplotlib.pyplot as plt
            import pandas as pd
            import os

            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Trading Performance Comparison: M30 vs M60', fontsize=16, fontweight='bold')

            # รวมข้อมูลจากทั้งสอง timeframe
            all_symbols = set()
            if 'M30' in timeframes:
                all_symbols.update(timeframes['M30'].keys())
            if 'M60' in timeframes:
                all_symbols.update(timeframes['M60'].keys())

            all_symbols = sorted(list(all_symbols))

            # เตรียมข้อมูลสำหรับเปรียบเทียบ
            m30_profits = []
            m60_profits = []
            m30_win_rates = []
            m60_win_rates = []

            for symbol in all_symbols:
                # M30 data
                m30_data = timeframes.get('M30', {}).get(symbol, {})
                m30_profits.append(m30_data.get('total_profit', 0))
                m30_win_rates.append(m30_data.get('win_rate', 0) * 100)

                # M60 data
                m60_data = timeframes.get('M60', {}).get(symbol, {})
                m60_profits.append(m60_data.get('total_profit', 0))
                m60_win_rates.append(m60_data.get('win_rate', 0) * 100)

            # กราฟ 1: Profit Comparison
            x = range(len(all_symbols))
            width = 0.35
            axes[0, 0].bar([i - width/2 for i in x], m30_profits, width, label='M30', alpha=0.8)
            axes[0, 0].bar([i + width/2 for i in x], m60_profits, width, label='M60', alpha=0.8)
            axes[0, 0].set_title('Profit Comparison: M30 vs M60')
            axes[0, 0].set_ylabel('Profit ($)')
            axes[0, 0].set_xticks(x)
            axes[0, 0].set_xticklabels(all_symbols, rotation=45)
            axes[0, 0].legend()
            axes[0, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)

            # กราฟ 2: Win Rate Comparison
            axes[0, 1].bar([i - width/2 for i in x], m30_win_rates, width, label='M30', alpha=0.8)
            axes[0, 1].bar([i + width/2 for i in x], m60_win_rates, width, label='M60', alpha=0.8)
            axes[0, 1].set_title('Win Rate Comparison: M30 vs M60')
            axes[0, 1].set_ylabel('Win Rate (%)')
            axes[0, 1].set_xticks(x)
            axes[0, 1].set_xticklabels(all_symbols, rotation=45)
            axes[0, 1].legend()
            axes[0, 1].axhline(y=50, color='red', linestyle='--', alpha=0.5)

            # กราฟ 3: Scatter Plot - Profit vs Win Rate
            axes[1, 0].scatter(m30_win_rates, m30_profits, label='M30', alpha=0.7, s=100)
            axes[1, 0].scatter(m60_win_rates, m60_profits, label='M60', alpha=0.7, s=100)
            axes[1, 0].set_title('Profit vs Win Rate')
            axes[1, 0].set_xlabel('Win Rate (%)')
            axes[1, 0].set_ylabel('Profit ($)')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            # กราฟ 4: Combined Performance Score
            combined_scores = []
            for i, symbol in enumerate(all_symbols):
                # คำนวณคะแนนรวม (normalized profit + win rate)
                m30_score = (m30_profits[i] / 1000) + m30_win_rates[i]  # ปรับ scale
                m60_score = (m60_profits[i] / 1000) + m60_win_rates[i]
                combined_scores.append((m30_score + m60_score) / 2)

            colors = ['green' if score > 50 else 'red' for score in combined_scores]
            axes[1, 1].bar(all_symbols, combined_scores, color=colors, alpha=0.7)
            axes[1, 1].set_title('Combined Performance Score')
            axes[1, 1].set_ylabel('Score')
            axes[1, 1].tick_params(axis='x', rotation=45)

            plt.tight_layout()

            # บันทึกกราฟ
            chart_path = os.path.join(self.results_folder, 'trading_performance_M30_vs_M60.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"✅ บันทึกกราฟเปรียบเทียบที่: {chart_path}")

            # สร้างไฟล์ CSV เปรียบเทียบ
            comparison_data = []
            for i, symbol in enumerate(all_symbols):
                comparison_data.append({
                    'Symbol': symbol,
                    'M30_Profit': m30_profits[i],
                    'M60_Profit': m60_profits[i],
                    'M30_Win_Rate': m30_win_rates[i] / 100,  # แปลงกลับเป็น decimal
                    'M60_Win_Rate': m60_win_rates[i] / 100,
                    'Combined_Score': combined_scores[i]
                })

            comparison_df = pd.DataFrame(comparison_data)
            csv_path = os.path.join(self.results_folder, 'trading_comparison_M30_vs_M60.csv')
            comparison_df.to_csv(csv_path, index=False)
            print(f"✅ บันทึกไฟล์ CSV เปรียบเทียบที่: {csv_path}")

        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการสร้างกราฟเปรียบเทียบ: {e}")
            import traceback
            traceback.print_exc()

    def run_complete_analysis(self, account_balance: float = 1000):
        """รันการวิเคราะห์ทั้งหมด"""

        if self.logger:
            self.logger.info("🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์", {'account_balance': account_balance})
        else:
            print("🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์")

        try:
            # รวมข้อมูลทั้งหมด
            combined_analysis = self.combine_all_analyses()
            if not combined_analysis:
                error_msg = "ไม่มีข้อมูลสำหรับการวิเคราะห์"
                if self.logger:
                    self.logger.warning(error_msg, {'symbols_available': len(self.symbol_data)})
                else:
                    print(f"❌ {error_msg}")
                return

            print(f"📊 พบข้อมูลการเทรด: {combined_analysis['total_trades']} รายการ")

            # สร้างกราฟ
            self.create_performance_plots(combined_analysis)

            # คำนวณความเสี่ยง
            risk_analysis = self.calculate_risk_management(combined_analysis, account_balance)

            # สร้างตารางความเสี่ยง
            risk_table = self.create_risk_table(risk_analysis)

            # บันทึกผลการวิเคราะห์
            self.save_complete_analysis(combined_analysis, risk_analysis, risk_table)

            # สร้างกราฟแยกรายละเอียดตาม timeframe และ symbol
            self.create_enhanced_financial_charts()

            # แสดงผลสรุป
            summary_data = {
                'account_balance': account_balance,
                'total_trades': combined_analysis['total_trades'],
                'total_profit_usd': combined_analysis['total_profit_usd'],
                'max_drawdown_usd': combined_analysis['max_drawdown_usd'],
                'recommended_lot_size': risk_analysis['recommended_lot_size'],
                'max_risk_percentage': risk_analysis['max_risk_percentage']
            }

            if self.logger:
                self.logger.info("✅ การวิเคราะห์ทางการเงินเสร็จสมบูรณ์", summary_data)

            print("\n" + "=" * 60)
            print("📈 FINANCIAL ANALYSIS SUMMARY")
            print("=" * 60)
            print(f"💰 Account Balance: ${account_balance:,.2f}")
            print(f"📊 Total Trades: {combined_analysis['total_trades']}")
            print(f"💵 Total Profit (1.0 lot): ${combined_analysis['total_profit_usd']:,.2f}")
            print(f"📉 Max Drawdown (1.0 lot): ${combined_analysis['max_drawdown_usd']:,.2f}")
            print(f"🎯 Recommended Lot Size: {risk_analysis['recommended_lot_size']:.4f}")
            print(f"⚠️ Max Risk: {risk_analysis['max_risk_percentage']:.2f}%")
            print("=" * 60)

            return {
                'combined_analysis': combined_analysis,
                'risk_analysis': risk_analysis,
                'risk_table': risk_table
            }

        except Exception as e:
            if self.logger:
                self.logger.error("ข้อผิดพลาดในการวิเคราะห์ทางการเงิน", e, {
                    'account_balance': account_balance,
                    'symbols_count': len(self.symbol_data)
                })
            else:
                print(f"❌ ข้อผิดพลาดในการวิเคราะห์ทางการเงิน: {e}")
                import traceback
                traceback.print_exc()
            return None
